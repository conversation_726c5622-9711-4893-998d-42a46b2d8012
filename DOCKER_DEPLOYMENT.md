# Docker Deployment Guide

This guide covers deploying TestLink Manager with TestLink using Docker Compose.

## 🚀 Quick Start

### 1. Automated Setup (Recommended)

```bash
# Clone the repository
git clone <repository-url>
cd testlink_custom

# Run automated setup
chmod +x setup_testlink.sh
./setup_testlink.sh
```

### 2. Manual Setup

```bash
# Start all services
docker-compose up -d

# Check service status
docker-compose ps

# View logs
docker-compose logs -f
```

## 📋 Services Overview

| Service | Port | Description | URL |
|---------|------|-------------|-----|
| TestLink Manager | 8000 | Django application | http://localhost:8000 |
| TestLink | 8080 | TestLink application | http://localhost:8080 |
| PostgreSQL | 5432 | TestLink Manager database | localhost:5432 |
| MySQL | 3306 | TestLink database | localhost:3306 |
| Nginx | 80, 8090 | Reverse proxy & health check | http://localhost:80 |

## 🔧 Configuration

### Environment Variables

Create a `.env` file for production:

```env
# Django Settings
SECRET_KEY=your-super-secret-key-here
DEBUG=False
ALLOWED_HOSTS=localhost,127.0.0.1,your-domain.com

# Database Passwords
DB_PASSWORD=secure_postgres_password
TESTLINK_DB_PASSWORD=secure_mysql_password
MYSQL_ROOT_PASSWORD=secure_root_password

# TestLink Integration
TESTLINK_API_KEY=your-testlink-api-key

# Email Configuration (optional)
EMAIL_HOST=smtp.gmail.com
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-email-password
```

### Production Deployment

```bash
# Use production configuration
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
```

## 🔗 TestLink Integration Setup

### Step 1: Complete TestLink Installation

1. Visit http://localhost:8080
2. Follow the TestLink installation wizard
3. Configure database connection (already set up in Docker)
4. Create admin user

### Step 2: Enable XML-RPC API

1. Login to TestLink as admin
2. Go to **System** → **Configuration**
3. Find **API** section
4. Enable **"Enable TestLink XML-RPC API"**
5. Save configuration

### Step 3: Generate API Key

1. Go to your user profile in TestLink
2. Click **"API interface"** tab
3. Click **"Generate a new key"**
4. Copy the generated API key

### Step 4: Update TestLink Manager

```bash
# Method 1: Update docker-compose.yml
# Edit TESTLINK_API_KEY in docker-compose.yml

# Method 2: Use environment variable
export TESTLINK_API_KEY=your-api-key-here
docker-compose restart web

# Method 3: Update .env file
echo "TESTLINK_API_KEY=your-api-key-here" >> .env
docker-compose restart web
```

### Step 5: Test Integration

```bash
# Test connection
docker-compose exec web python manage.py setup_testlink_integration --test-connection

# Sync data from TestLink
docker-compose exec web python manage.py sync_testlink

# Check data status
docker-compose exec web python manage.py check_data
```

## 🔍 Health Monitoring

### Health Check Script

```bash
# Run comprehensive health check
./health_check.sh
```

### Manual Health Checks

```bash
# Check service status
docker-compose ps

# Check logs
docker-compose logs web
docker-compose logs testlink
docker-compose logs db
docker-compose logs testlink_db

# Check resource usage
docker stats

# Test endpoints
curl http://localhost:8000/
curl http://localhost:8080/
curl http://localhost:8090/health
```

## 🛠️ Maintenance

### Backup Data

```bash
# Backup TestLink Manager database
docker-compose exec db pg_dump -U testlink_user testlink_manager > backup_manager.sql

# Backup TestLink database
docker-compose exec testlink_db mysqldump -u testlink -ptestlink123 testlink > backup_testlink.sql

# Backup volumes
docker run --rm -v testlink_custom_testlink_data:/data -v $(pwd):/backup alpine tar czf /backup/testlink_data.tar.gz -C /data .
```

### Restore Data

```bash
# Restore TestLink Manager database
cat backup_manager.sql | docker-compose exec -T db psql -U testlink_user testlink_manager

# Restore TestLink database
cat backup_testlink.sql | docker-compose exec -T testlink_db mysql -u testlink -ptestlink123 testlink
```

### Update Services

```bash
# Pull latest images
docker-compose pull

# Restart services
docker-compose down
docker-compose up -d

# Update TestLink Manager code
git pull
docker-compose build web
docker-compose up -d web
```

### Scale Services

```bash
# Scale web service
docker-compose up -d --scale web=3

# Use load balancer (update nginx.conf)
docker-compose restart nginx
```

## 🐛 Troubleshooting

### Common Issues

#### 1. TestLink Manager can't connect to TestLink

```bash
# Check if TestLink is running
curl http://localhost:8080

# Check API endpoint
curl http://localhost:8080/lib/api/xmlrpc/v1/xmlrpc.php

# Verify API is enabled in TestLink configuration
# Check logs
docker-compose logs testlink
```

#### 2. Database Connection Issues

```bash
# Check database status
docker-compose ps db testlink_db

# Test connections
docker-compose exec web python manage.py dbshell
docker-compose exec testlink_db mysql -u testlink -ptestlink123 testlink

# Check logs
docker-compose logs db
docker-compose logs testlink_db
```

#### 3. Permission Issues

```bash
# Fix file permissions
sudo chown -R $USER:$USER .

# Fix volume permissions
docker-compose exec web chown -R appuser:appuser /app
```

#### 4. Port Conflicts

```bash
# Check what's using ports
sudo lsof -i :8000
sudo lsof -i :8080
sudo lsof -i :5432
sudo lsof -i :3306

# Change ports in docker-compose.yml if needed
```

### Logs and Debugging

```bash
# View all logs
docker-compose logs

# Follow logs in real-time
docker-compose logs -f

# View specific service logs
docker-compose logs web
docker-compose logs testlink

# Debug inside containers
docker-compose exec web bash
docker-compose exec testlink bash
```

## 🔒 Security Considerations

### Production Security

1. **Change default passwords**
2. **Use strong API keys**
3. **Enable HTTPS** (add SSL certificates to nginx)
4. **Restrict database access**
5. **Use secrets management** for sensitive data
6. **Regular security updates**

### Network Security

```bash
# Create custom network
docker network create testlink_network

# Update docker-compose.yml to use custom network
# Restrict external access to databases
```

## 📊 Monitoring

### Prometheus + Grafana (Optional)

```yaml
# Add to docker-compose.yml
prometheus:
  image: prom/prometheus
  ports:
    - "9090:9090"

grafana:
  image: grafana/grafana
  ports:
    - "3000:3000"
```

### Log Aggregation

```bash
# Use centralized logging
docker-compose logs | logger -t testlink-manager
```

## 🚀 Performance Optimization

### Database Optimization

```bash
# PostgreSQL tuning
# Add to docker-compose.yml under db service:
command: postgres -c shared_preload_libraries=pg_stat_statements -c pg_stat_statements.track=all

# MySQL tuning
# Add to docker-compose.yml under testlink_db service:
command: --innodb-buffer-pool-size=256M --max-connections=200
```

### Application Optimization

```bash
# Use production WSGI server
# Already configured with gunicorn in production

# Enable caching
# Add Redis service and configure Django caching
```

This deployment guide provides a comprehensive setup for running TestLink Manager with TestLink in a Docker environment.
