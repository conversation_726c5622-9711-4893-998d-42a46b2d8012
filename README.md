# TestLink Manager

A Django web application for managing TestLink test assignments and reporting. This application provides a modern web interface for assigning test cases to users and generating comprehensive test execution reports.

## Features

- **Test Assignment Management**: Assign test cases to users with due dates and notes
- **Bulk Assignment**: Assign multiple test cases to users at once
- **Test Execution**: Record test execution results with detailed notes
- **Comprehensive Reporting**:
  - Dashboard with statistics and charts
  - Test progress reports
  - User performance reports
  - Test case execution reports
- **TestLink Integration**: Sync data with TestLink via XML-RPC API
- **Modern UI**: Built with AdminLTE 3.2.0 for a professional look
- **Role-based Access**: Different permission levels for testers, test leads, and admins

## Requirements

- Python 3.8+
- Django 4.2+
- TestLink installation with XML-RPC API enabled
- Modern web browser

## Installation

### 1. Clone the repository

```bash
git clone <repository-url>
cd testlink_custom
```

### 2. Create virtual environment

```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

### 3. Install dependencies

```bash
pip install -r requirements.txt
```

### 4. Configure settings

Create a `.env` file in the project root:

```env
SECRET_KEY=your-secret-key-here
DEBUG=True
TESTLINK_URL=http://your-testlink-server/lib/api/xmlrpc/v1/xmlrpc.php
TESTLINK_API_KEY=your-testlink-api-key
```

### 5. Run migrations

```bash
python manage.py makemigrations
python manage.py migrate
```

### 6. Create superuser

```bash
python manage.py createsuperuser
```

### 7. Collect static files

```bash
python manage.py collectstatic
```

### 8. Run development server

```bash
python manage.py runserver
```

Visit `http://localhost:8000` to access the application.

## Quick Demo Setup

For a quick demo with sample data, use one of these methods:

### Method 1: Auto Setup Script

**Linux/Mac:**

```bash
chmod +x init_demo.sh
./init_demo.sh
```

**Windows:**

```cmd
init_demo.bat
```

### Method 2: Manual Setup with Demo Data

```bash
# Setup project
python setup.py

# Or create demo environment
python manage.py setup_demo
```

### Method 3: Create Custom Test Data

```bash
# Create test data only
python manage.py create_test_data

# Clear existing data and create new
python manage.py create_test_data --clear

# Load sample fixtures
python manage.py loaddata sample_data
```

### Demo Login Credentials

After running the demo setup, you can login with:

- **Admin**: `admin` / `admin123`
- **Test Lead**: `testlead` / `testpass123`
- **Testers**:
  - `john` / `testpass123`
  - `jane` / `testpass123`
  - `mike` / `testpass123`
  - `sarah` / `testpass123`
  - `david` / `testpass123`

## Configuration

### TestLink API Setup

1. Enable XML-RPC API in TestLink:

   - Go to TestLink admin panel
   - Navigate to System > Configuration
   - Enable "Enable TestLink XML-RPC API"

2. Generate API key:
   - Go to your user profile in TestLink
   - Generate an API key
   - Add this key to your `.env` file

### User Roles

The application supports three user roles:

- **Tester**: Can execute assigned tests and view own reports
- **Test Lead**: Can assign tests, view all reports, and manage team
- **Admin**: Full access to all features

## Usage

### 1. Initial Setup

1. Login with your superuser account
2. Go to Django admin panel (`/admin/`)
3. Create TestLink projects, test plans, and test cases
4. Create user profiles and assign roles
5. Sync data with TestLink (optional)

### 2. Assigning Tests

1. Navigate to "Test Assignments" > "Create Assignment"
2. Select test plan, test case, and assigned user
3. Set due date and add notes if needed
4. Click "Create Assignment"

For bulk assignments:

1. Go to "Test Assignments" > "Bulk Assignment"
2. Select test plan and multiple test cases
3. Choose assigned user and click "Create Assignments"

### 3. Executing Tests

1. Go to "Test Assignments" to see your assigned tests
2. Click the "Execute" button for a test
3. Select build, set status, and add execution notes
4. Click "Save Execution"

### 4. Viewing Reports

- **Dashboard**: Overview of statistics and recent activity
- **Test Progress**: Progress by test plan
- **User Performance**: Performance metrics by user
- **Test Cases**: Detailed test case execution results

## API Integration

The application integrates with TestLink via XML-RPC API:

- Sync projects, test plans, and test cases
- Push test assignments to TestLink
- Report test execution results back to TestLink

## Development

### Running Tests

```bash
python manage.py test
```

### Code Style

The project follows Django coding standards. Use:

```bash
flake8 .
black .
```

### Database Migrations

When making model changes:

```bash
python manage.py makemigrations
python manage.py migrate
```

## Deployment

### Production Settings

1. Set `DEBUG=False` in settings
2. Configure proper database (PostgreSQL recommended)
3. Set up static file serving
4. Configure email settings
5. Use environment variables for sensitive data

### Using Docker

**TestLink Manager only:**

```bash
docker build -t testlink-manager .
docker run -p 8000:8000 testlink-manager
```

**Complete environment with TestLink:**

```bash
# Start all services (TestLink Manager + TestLink + Databases)
docker-compose up -d

# Or use setup script for guided setup
chmod +x setup_testlink.sh
./setup_testlink.sh
```

### Docker Services

The complete Docker setup includes:

- **TestLink Manager**: `http://localhost:8000` (Django app)
- **TestLink**: `http://localhost:8080` (TestLink application)
- **PostgreSQL**: `localhost:5432` (TestLink Manager database)
- **MySQL**: `localhost:3306` (TestLink database)
- **Nginx**: `localhost:80` (Reverse proxy)

### TestLink Integration Setup

After starting the Docker services:

1. **Complete TestLink setup:**

   - Visit `http://localhost:8080`
   - Follow TestLink installation wizard
   - Create admin user

2. **Enable XML-RPC API:**

   - Login to TestLink as admin
   - Go to System → Configuration
   - Enable "Enable TestLink XML-RPC API"

3. **Generate API key:**

   - Go to your user profile in TestLink
   - Generate an API key
   - Copy the key

4. **Update TestLink Manager configuration:**

   ```bash
   # Update API key in docker-compose.yml
   # Or set environment variable
   export TESTLINK_API_KEY=your-api-key-here
   docker-compose restart web
   ```

5. **Test integration:**
   ```bash
   docker-compose exec web python manage.py setup_testlink_integration --test-connection
   ```

## Troubleshooting

### Common Issues

1. **TestLink API Connection Failed**

   - Check TestLink URL and API key
   - Ensure XML-RPC API is enabled in TestLink
   - Verify network connectivity

2. **Permission Denied Errors**

   - Check user roles and permissions
   - Ensure user has TestLink profile created

3. **Static Files Not Loading**
   - Run `python manage.py collectstatic`
   - Check STATIC_URL and STATIC_ROOT settings

### Logs

Check Django logs for detailed error information:

```bash
tail -f logs/django.log
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:

- Create an issue on GitHub
- Check the documentation
- Contact the development team

## Changelog

### Version 1.0.0

- Initial release
- Basic test assignment functionality
- TestLink integration
- Reporting dashboard
- User management
