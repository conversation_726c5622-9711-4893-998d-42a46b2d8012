#!/usr/bin/env python
"""
Setup script for TestLink Manager
"""
import os
import sys
import django
from django.core.management import execute_from_command_line
from django.contrib.auth.models import User


def setup_project():
    """Setup the Django project"""
    print("Setting up TestLink Manager...")
    
    # Set Django settings
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'testlink_manager.settings')
    django.setup()
    
    # Run migrations
    print("Running migrations...")
    execute_from_command_line(['manage.py', 'makemigrations'])
    execute_from_command_line(['manage.py', 'migrate'])
    
    # Create superuser if it doesn't exist
    print("Creating superuser...")
    if not User.objects.filter(username='admin').exists():
        User.objects.create_superuser(
            username='admin',
            email='<EMAIL>',
            password='admin123'
        )
        print("Superuser created: admin/admin123")
    else:
        print("Superuser already exists")

    # Create test data
    print("Creating test data...")
    execute_from_command_line(['manage.py', 'create_test_data'])

    # Collect static files
    print("Collecting static files...")
    execute_from_command_line(['manage.py', 'collectstatic', '--noinput'])

    print("\n" + "="*50)
    print("Setup complete!")
    print("="*50)
    print("\nLogin credentials:")
    print("- Admin: admin/admin123")
    print("- Test Lead: testlead/testpass123")
    print("- Testers: john/testpass123, jane/testpass123, etc.")
    print("\nRun: python manage.py runserver")
    print("Visit: http://localhost:8000")


if __name__ == '__main__':
    setup_project()
