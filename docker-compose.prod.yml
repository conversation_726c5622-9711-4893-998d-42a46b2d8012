# Production Docker Compose configuration
version: '3.8'

services:
  web:
    environment:
      - DEBUG=False
      - ALLOWED_HOSTS=localhost,127.0.0.1,your-domain.com
      - SECRET_KEY=${SECRET_KEY}
      - DATABASE_URL=postgresql://testlink_user:${DB_PASSWORD}@db:5432/testlink_manager
      - TESTLINK_URL=http://testlink:80/lib/api/xmlrpc/v1/xmlrpc.php
      - TESTLINK_API_KEY=${TESTLINK_API_KEY}
      - EMAIL_HOST=${EMAIL_HOST}
      - EMAIL_HOST_USER=${EMAIL_HOST_USER}
      - EMAIL_HOST_PASSWORD=${EMAIL_HOST_PASSWORD}
    command: >
      sh -c "python manage.py migrate &&
             python manage.py collectstatic --noinput &&
             gunicorn --bind 0.0.0.0:8000 --workers 3 testlink_manager.wsgi:application"
    restart: unless-stopped

  db:
    environment:
      - POSTGRES_PASSWORD=${DB_PASSWORD}
    restart: unless-stopped

  testlink:
    environment:
      - DB_PASS=${TESTLINK_DB_PASSWORD}
      - MYSQL_PASSWORD=${TESTLINK_DB_PASSWORD}
    restart: unless-stopped

  testlink_db:
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - MYSQL_PASSWORD=${TESTLINK_DB_PASSWORD}
    restart: unless-stopped

  nginx:
    restart: unless-stopped
