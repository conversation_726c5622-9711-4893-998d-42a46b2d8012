---
layout: page
title: License
---

AdminLTE is an open source project that is licensed under the [MIT license](https://opensource.org/licenses/MIT). This allows you to do pretty much anything you want as long as you include the copyright in "all copies or substantial portions of the Software." Attribution is not required (though very much appreciated).

<h5 class="text-bold text-dark mt-3">What You Are <span class="text-success">Allowed</span> To Do With AdminLTE</h5>

- Use in commercial projects.
- Use in personal/private projects.
- Modify and change the work.
- Distribute the code.
- Sublicense: incorporate the work into something that has a more restrictive license.

<h5 class="text-bold text-dark mt-3">What You Are <span class="text-danger">Not Allowed</span> To Do With AdminLTE</h5>

- The work is provided "as is". You may not hold the author liable.

<h5 class="text-bold text-dark mt-3">What You <span class="text-warning">Must</span> Do When Using AdminLTE</h5>

- Include the license notice in all copies of the work.
- Include the copyright notice in all copies of the work. This applies to everything except the notice in the footer of the HTML example pages.
