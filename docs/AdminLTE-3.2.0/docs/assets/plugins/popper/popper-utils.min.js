/*
 Copyright (C) Federico <PERSON> 2020
 Distributed under the MIT License (license terms are at http://opensource.org/licenses/MIT).
 */function a(a,b){if(1!==a.nodeType)return[];const c=a.ownerDocument.defaultView,d=c.getComputedStyle(a,null);return b?d[b]:d}function b(a){return'HTML'===a.nodeName?a:a.parentNode||a.host}function c(d){if(!d)return document.body;switch(d.nodeName){case'HTML':case'BODY':return d.ownerDocument.body;case'#document':return d.body;}const{overflow:e,overflowX:f,overflowY:g}=a(d);return /(auto|scroll|overlay)/.test(e+g+f)?d:c(b(d))}function d(a){return a&&a.referenceNode?a.referenceNode:a}var e='undefined'!=typeof window&&'undefined'!=typeof document&&'undefined'!=typeof navigator;const f=e&&!!(window.MSInputMethodContext&&document.documentMode),g=e&&/MSIE 10/.test(navigator.userAgent);function h(a){return 11===a?f:10===a?g:f||g}function i(b){if(!b)return document.documentElement;const c=h(10)?document.body:null;let d=b.offsetParent||null;for(;d===c&&b.nextElementSibling;)d=(b=b.nextElementSibling).offsetParent;const e=d&&d.nodeName;return e&&'BODY'!==e&&'HTML'!==e?-1!==['TH','TD','TABLE'].indexOf(d.nodeName)&&'static'===a(d,'position')?i(d):d:b?b.ownerDocument.documentElement:document.documentElement}function j(a){const{nodeName:b}=a;return'BODY'!==b&&('HTML'===b||i(a.firstElementChild)===a)}function k(a){return null===a.parentNode?a:k(a.parentNode)}function l(a,b){if(!a||!a.nodeType||!b||!b.nodeType)return document.documentElement;const c=a.compareDocumentPosition(b)&Node.DOCUMENT_POSITION_FOLLOWING,d=c?a:b,e=c?b:a,f=document.createRange();f.setStart(d,0),f.setEnd(e,0);const{commonAncestorContainer:g}=f;if(a!==g&&b!==g||d.contains(e))return j(g)?g:i(g);const h=k(a);return h.host?l(h.host,b):l(a,k(b).host)}function m(a,b='top'){const c='top'===b?'scrollTop':'scrollLeft',d=a.nodeName;if('BODY'===d||'HTML'===d){const b=a.ownerDocument.documentElement,d=a.ownerDocument.scrollingElement||b;return d[c]}return a[c]}function n(a,b,c=!1){const d=m(b,'top'),e=m(b,'left'),f=c?-1:1;return a.top+=d*f,a.bottom+=d*f,a.left+=e*f,a.right+=e*f,a}function o(a,b){const c='x'===b?'Left':'Top',d='Left'==c?'Right':'Bottom';return parseFloat(a[`border${c}Width`])+parseFloat(a[`border${d}Width`])}function p(a,b,c,d){return Math.max(b[`offset${a}`],b[`scroll${a}`],c[`client${a}`],c[`offset${a}`],c[`scroll${a}`],h(10)?parseInt(c[`offset${a}`])+parseInt(d[`margin${'Height'===a?'Top':'Left'}`])+parseInt(d[`margin${'Height'===a?'Bottom':'Right'}`]):0)}function q(a){const b=a.body,c=a.documentElement,d=h(10)&&getComputedStyle(c);return{height:p('Height',b,c,d),width:p('Width',b,c,d)}}var r=Object.assign||function(a){for(var b,c=1;c<arguments.length;c++)for(var d in b=arguments[c],b)Object.prototype.hasOwnProperty.call(b,d)&&(a[d]=b[d]);return a};function s(a){return r({},a,{right:a.left+a.width,bottom:a.top+a.height})}function t(b){let c={};try{if(h(10)){c=b.getBoundingClientRect();const a=m(b,'top'),d=m(b,'left');c.top+=a,c.left+=d,c.bottom+=a,c.right+=d}else c=b.getBoundingClientRect()}catch(a){}const d={left:c.left,top:c.top,width:c.right-c.left,height:c.bottom-c.top},e='HTML'===b.nodeName?q(b.ownerDocument):{},f=e.width||b.clientWidth||d.width,g=e.height||b.clientHeight||d.height;let i=b.offsetWidth-f,j=b.offsetHeight-g;if(i||j){const c=a(b);i-=o(c,'x'),j-=o(c,'y'),d.width-=i,d.height-=j}return s(d)}function u(b,d,e=!1){var f=Math.max;const g=h(10),i='HTML'===d.nodeName,j=t(b),k=t(d),l=c(b),m=a(d),o=parseFloat(m.borderTopWidth),p=parseFloat(m.borderLeftWidth);e&&i&&(k.top=f(k.top,0),k.left=f(k.left,0));let q=s({top:j.top-k.top-o,left:j.left-k.left-p,width:j.width,height:j.height});if(q.marginTop=0,q.marginLeft=0,!g&&i){const a=parseFloat(m.marginTop),b=parseFloat(m.marginLeft);q.top-=o-a,q.bottom-=o-a,q.left-=p-b,q.right-=p-b,q.marginTop=a,q.marginLeft=b}return(g&&!e?d.contains(l):d===l&&'BODY'!==l.nodeName)&&(q=n(q,d)),q}function v(a,b=!1){var c=Math.max;const d=a.ownerDocument.documentElement,e=u(a,d),f=c(d.clientWidth,window.innerWidth||0),g=c(d.clientHeight,window.innerHeight||0),h=b?0:m(d),i=b?0:m(d,'left'),j={top:h-e.top+e.marginTop,left:i-e.left+e.marginLeft,width:f,height:g};return s(j)}function w(c){const d=c.nodeName;if('BODY'===d||'HTML'===d)return!1;if('fixed'===a(c,'position'))return!0;const e=b(c);return!!e&&w(e)}function x(b){if(!b||!b.parentElement||h())return document.documentElement;let c=b.parentElement;for(;c&&'none'===a(c,'transform');)c=c.parentElement;return c||document.documentElement}function y(a,e,f,g,h=!1){let i={top:0,left:0};const j=h?x(a):l(a,d(e));if('viewport'===g)i=v(j,h);else{let d;'scrollParent'===g?(d=c(b(e)),'BODY'===d.nodeName&&(d=a.ownerDocument.documentElement)):'window'===g?d=a.ownerDocument.documentElement:d=g;const f=u(d,j,h);if('HTML'===d.nodeName&&!w(j)){const{height:b,width:c}=q(a.ownerDocument);i.top+=f.top-f.marginTop,i.bottom=b+f.top,i.left+=f.left-f.marginLeft,i.right=c+f.left}else i=f}f=f||0;const k='number'==typeof f;return i.left+=k?f:f.left||0,i.top+=k?f:f.top||0,i.right-=k?f:f.right||0,i.bottom-=k?f:f.bottom||0,i}function z({width:a,height:b}){return a*b}function A(a,b,c,d,e,f=0){if(-1===a.indexOf('auto'))return a;const g=y(c,d,f,e),h={top:{width:g.width,height:b.top-g.top},right:{width:g.right-b.right,height:g.height},bottom:{width:g.width,height:g.bottom-b.bottom},left:{width:b.left-g.left,height:g.height}},i=Object.keys(h).map((a)=>r({key:a},h[a],{area:z(h[a])})).sort((c,a)=>a.area-c.area),j=i.filter(({width:a,height:b})=>a>=c.clientWidth&&b>=c.clientHeight),k=0<j.length?j[0].key:i[0].key,l=a.split('-')[1];return k+(l?`-${l}`:'')}const B=function(){const a=['Edge','Trident','Firefox'];for(let b=0;b<a.length;b+=1)if(e&&0<=navigator.userAgent.indexOf(a[b]))return 1;return 0}();function C(a){let b=!1;return()=>{b||(b=!0,window.Promise.resolve().then(()=>{b=!1,a()}))}}function D(a){let b=!1;return()=>{b||(b=!0,setTimeout(()=>{b=!1,a()},B))}}const E=e&&window.Promise;var F=E?C:D;function G(a,b){return Array.prototype.find?a.find(b):a.filter(b)[0]}function H(a,b,c){if(Array.prototype.findIndex)return a.findIndex((a)=>a[b]===c);const d=G(a,(a)=>a[b]===c);return a.indexOf(d)}function I(a){let b;if('HTML'===a.nodeName){const{width:c,height:d}=q(a.ownerDocument);b={width:c,height:d,left:0,top:0}}else b={width:a.offsetWidth,height:a.offsetHeight,left:a.offsetLeft,top:a.offsetTop};return s(b)}function J(a){const b=a.ownerDocument.defaultView,c=b.getComputedStyle(a),d=parseFloat(c.marginTop||0)+parseFloat(c.marginBottom||0),e=parseFloat(c.marginLeft||0)+parseFloat(c.marginRight||0),f={width:a.offsetWidth+e,height:a.offsetHeight+d};return f}function K(a){const b={left:'right',right:'left',bottom:'top',top:'bottom'};return a.replace(/left|right|bottom|top/g,(a)=>b[a])}function L(a,b,c){c=c.split('-')[0];const d=J(a),e={width:d.width,height:d.height},f=-1!==['right','left'].indexOf(c),g=f?'top':'left',h=f?'left':'top',i=f?'height':'width',j=f?'width':'height';return e[g]=b[g]+b[i]/2-d[i]/2,e[h]=c===h?b[h]-d[j]:b[K(h)],e}function M(a,b,c,e=null){const f=e?x(b):l(b,d(c));return u(c,f,e)}function N(a){const b=[!1,'ms','Webkit','Moz','O'],c=a.charAt(0).toUpperCase()+a.slice(1);for(let d=0;d<b.length;d++){const e=b[d],f=e?`${e}${c}`:a;if('undefined'!=typeof document.body.style[f])return f}return null}function O(a){return a&&'[object Function]'==={}.toString.call(a)}function P(a,b){return a.some(({name:a,enabled:c})=>c&&a===b)}function Q(a,b,c){const d=G(a,({name:a})=>a===b),e=!!d&&a.some((a)=>a.name===c&&a.enabled&&a.order<d.order);if(!e){const a=`\`${b}\``,d=`\`${c}\``;console.warn(`${d} modifier is required by ${a} modifier in order to work, be sure to include it before ${a}!`)}return e}function R(a){return''!==a&&!isNaN(parseFloat(a))&&isFinite(a)}function S(a){const b=a.ownerDocument;return b?b.defaultView:window}function T(a,b){return S(a).removeEventListener('resize',b.updateBound),b.scrollParents.forEach((a)=>{a.removeEventListener('scroll',b.updateBound)}),b.updateBound=null,b.scrollParents=[],b.scrollElement=null,b.eventsEnabled=!1,b}function U(a,b,c){const d=void 0===c?a:a.slice(0,H(a,'name',c));return d.forEach((a)=>{a['function']&&console.warn('`modifier.function` is deprecated, use `modifier.fn`!');const c=a['function']||a.fn;a.enabled&&O(c)&&(b.offsets.popper=s(b.offsets.popper),b.offsets.reference=s(b.offsets.reference),b=c(b,a))}),b}function V(a,b){Object.keys(b).forEach(function(c){const d=b[c];!1===d?a.removeAttribute(c):a.setAttribute(c,b[c])})}function W(a,b){Object.keys(b).forEach((c)=>{let d='';-1!==['width','height','top','right','bottom','left'].indexOf(c)&&R(b[c])&&(d='px'),a.style[c]=b[c]+d})}function X(a,b,d,e){const f='BODY'===a.nodeName,g=f?a.ownerDocument.defaultView:a;g.addEventListener(b,d,{passive:!0}),f||X(c(g.parentNode),b,d,e),e.push(g)}function Y(a,b,d,e){d.updateBound=e,S(a).addEventListener('resize',d.updateBound,{passive:!0});const f=c(a);return X(f,'scroll',d.updateBound,d.scrollParents),d.scrollElement=f,d.eventsEnabled=!0,d}var Z={computeAutoPlacement:A,debounce:F,findIndex:H,getBordersSize:o,getBoundaries:y,getBoundingClientRect:t,getClientRect:s,getOffsetParent:i,getOffsetRect:I,getOffsetRectRelativeToArbitraryNode:u,getOuterSizes:J,getParentNode:b,getPopperOffsets:L,getReferenceOffsets:M,getScroll:m,getScrollParent:c,getStyleComputedProperty:a,getSupportedPropertyName:N,getWindowSizes:q,isFixed:w,isFunction:O,isModifierEnabled:P,isModifierRequired:Q,isNumeric:R,removeEventListeners:T,runModifiers:U,setAttributes:V,setStyles:W,setupEventListeners:Y};export{A as computeAutoPlacement,F as debounce,H as findIndex,o as getBordersSize,y as getBoundaries,t as getBoundingClientRect,s as getClientRect,i as getOffsetParent,I as getOffsetRect,u as getOffsetRectRelativeToArbitraryNode,J as getOuterSizes,b as getParentNode,L as getPopperOffsets,M as getReferenceOffsets,m as getScroll,c as getScrollParent,a as getStyleComputedProperty,N as getSupportedPropertyName,q as getWindowSizes,w as isFixed,O as isFunction,P as isModifierEnabled,Q as isModifierRequired,R as isNumeric,T as removeEventListeners,U as runModifiers,V as setAttributes,W as setStyles,Y as setupEventListeners};export default Z;
//# sourceMappingURL=popper-utils.min.js.map
