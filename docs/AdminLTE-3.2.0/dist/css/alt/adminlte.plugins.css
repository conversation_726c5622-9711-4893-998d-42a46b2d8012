/*!
 *   AdminLTE v3.2.0
 *     Only Plugins
 *   Author: Colorlib
 *   Website: AdminLTE.io <https://adminlte.io>
 *   License: Open source - MIT <https://opensource.org/licenses/MIT>
 */
@-webkit-keyframes flipInX {
  0% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    transition-timing-function: ease-in;
    opacity: 0;
  }
  40% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    transition-timing-function: ease-in;
  }
  60% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
    transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
    opacity: 1;
  }
  80% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -5deg);
    transform: perspective(400px) rotate3d(1, 0, 0, -5deg);
  }
  100% {
    -webkit-transform: perspective(400px);
    transform: perspective(400px);
  }
}
@keyframes flipInX {
  0% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    transition-timing-function: ease-in;
    opacity: 0;
  }
  40% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    transition-timing-function: ease-in;
  }
  60% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
    transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
    opacity: 1;
  }
  80% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -5deg);
    transform: perspective(400px) rotate3d(1, 0, 0, -5deg);
  }
  100% {
    -webkit-transform: perspective(400px);
    transform: perspective(400px);
  }
}

@-webkit-keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@-webkit-keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@-webkit-keyframes shake {
  0% {
    -webkit-transform: translate(2px, 1px) rotate(0deg);
    transform: translate(2px, 1px) rotate(0deg);
  }
  10% {
    -webkit-transform: translate(-1px, -2px) rotate(-2deg);
    transform: translate(-1px, -2px) rotate(-2deg);
  }
  20% {
    -webkit-transform: translate(-3px, 0) rotate(3deg);
    transform: translate(-3px, 0) rotate(3deg);
  }
  30% {
    -webkit-transform: translate(0, 2px) rotate(0deg);
    transform: translate(0, 2px) rotate(0deg);
  }
  40% {
    -webkit-transform: translate(1px, -1px) rotate(1deg);
    transform: translate(1px, -1px) rotate(1deg);
  }
  50% {
    -webkit-transform: translate(-1px, 2px) rotate(-1deg);
    transform: translate(-1px, 2px) rotate(-1deg);
  }
  60% {
    -webkit-transform: translate(-3px, 1px) rotate(0deg);
    transform: translate(-3px, 1px) rotate(0deg);
  }
  70% {
    -webkit-transform: translate(2px, 1px) rotate(-2deg);
    transform: translate(2px, 1px) rotate(-2deg);
  }
  80% {
    -webkit-transform: translate(-1px, -1px) rotate(4deg);
    transform: translate(-1px, -1px) rotate(4deg);
  }
  90% {
    -webkit-transform: translate(2px, 2px) rotate(0deg);
    transform: translate(2px, 2px) rotate(0deg);
  }
  100% {
    -webkit-transform: translate(1px, -2px) rotate(-1deg);
    transform: translate(1px, -2px) rotate(-1deg);
  }
}

@keyframes shake {
  0% {
    -webkit-transform: translate(2px, 1px) rotate(0deg);
    transform: translate(2px, 1px) rotate(0deg);
  }
  10% {
    -webkit-transform: translate(-1px, -2px) rotate(-2deg);
    transform: translate(-1px, -2px) rotate(-2deg);
  }
  20% {
    -webkit-transform: translate(-3px, 0) rotate(3deg);
    transform: translate(-3px, 0) rotate(3deg);
  }
  30% {
    -webkit-transform: translate(0, 2px) rotate(0deg);
    transform: translate(0, 2px) rotate(0deg);
  }
  40% {
    -webkit-transform: translate(1px, -1px) rotate(1deg);
    transform: translate(1px, -1px) rotate(1deg);
  }
  50% {
    -webkit-transform: translate(-1px, 2px) rotate(-1deg);
    transform: translate(-1px, 2px) rotate(-1deg);
  }
  60% {
    -webkit-transform: translate(-3px, 1px) rotate(0deg);
    transform: translate(-3px, 1px) rotate(0deg);
  }
  70% {
    -webkit-transform: translate(2px, 1px) rotate(-2deg);
    transform: translate(2px, 1px) rotate(-2deg);
  }
  80% {
    -webkit-transform: translate(-1px, -1px) rotate(4deg);
    transform: translate(-1px, -1px) rotate(4deg);
  }
  90% {
    -webkit-transform: translate(2px, 2px) rotate(0deg);
    transform: translate(2px, 2px) rotate(0deg);
  }
  100% {
    -webkit-transform: translate(1px, -2px) rotate(-1deg);
    transform: translate(1px, -2px) rotate(-1deg);
  }
}

@-webkit-keyframes wobble {
  0% {
    -webkit-transform: none;
    transform: none;
  }
  15% {
    -webkit-transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg);
    transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg);
  }
  30% {
    -webkit-transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg);
    transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg);
  }
  45% {
    -webkit-transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg);
    transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg);
  }
  60% {
    -webkit-transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg);
    transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg);
  }
  75% {
    -webkit-transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg);
    transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg);
  }
  100% {
    -webkit-transform: none;
    transform: none;
  }
}

@keyframes wobble {
  0% {
    -webkit-transform: none;
    transform: none;
  }
  15% {
    -webkit-transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg);
    transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg);
  }
  30% {
    -webkit-transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg);
    transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg);
  }
  45% {
    -webkit-transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg);
    transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg);
  }
  60% {
    -webkit-transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg);
    transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg);
  }
  75% {
    -webkit-transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg);
    transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg);
  }
  100% {
    -webkit-transform: none;
    transform: none;
  }
}

.fc-button {
  background: #f8f9fa;
  background-image: none;
  border-bottom-color: #ddd;
  border-color: #ddd;
  color: #495057;
}

.fc-button:hover, .fc-button:active, .fc-button.hover {
  background-color: #e9e9e9;
}

.fc-header-title h2 {
  color: #666;
  font-size: 15px;
  line-height: 1.6em;
  margin-left: 10px;
}

.fc-header-right {
  padding-right: 10px;
}

.fc-header-left {
  padding-left: 10px;
}

.fc-widget-header {
  background: #fafafa;
}

.fc-grid {
  border: 0;
  width: 100%;
}

.fc-widget-header:first-of-type,
.fc-widget-content:first-of-type {
  border-left: 0;
  border-right: 0;
}

.fc-widget-header:last-of-type,
.fc-widget-content:last-of-type {
  border-right: 0;
}

.fc-toolbar,
.fc-toolbar.fc-header-toolbar {
  margin: 0;
  padding: 1rem;
}

@media (max-width: 575.98px) {
  .fc-toolbar {
    -ms-flex-direction: column;
    flex-direction: column;
  }
  .fc-toolbar .fc-left {
    -ms-flex-order: 1;
    order: 1;
    margin-bottom: .5rem;
  }
  .fc-toolbar .fc-center {
    -ms-flex-order: 0;
    order: 0;
    margin-bottom: .375rem;
  }
  .fc-toolbar .fc-right {
    -ms-flex-order: 2;
    order: 2;
  }
}

.fc-day-number {
  font-size: 20px;
  font-weight: 300;
  padding-right: 10px;
}

.fc-color-picker {
  list-style: none;
  margin: 0;
  padding: 0;
}

.fc-color-picker > li {
  float: left;
  font-size: 30px;
  line-height: 30px;
  margin-right: 5px;
}

.fc-color-picker > li .fa,
.fc-color-picker > li .fas,
.fc-color-picker > li .far,
.fc-color-picker > li .fab,
.fc-color-picker > li .fal,
.fc-color-picker > li .fad,
.fc-color-picker > li .svg-inline--fa,
.fc-color-picker > li .ion {
  transition: -webkit-transform linear .3s;
  transition: transform linear .3s;
  transition: transform linear .3s, -webkit-transform linear .3s;
}

.fc-color-picker > li .fa:hover,
.fc-color-picker > li .fas:hover,
.fc-color-picker > li .far:hover,
.fc-color-picker > li .fab:hover,
.fc-color-picker > li .fal:hover,
.fc-color-picker > li .fad:hover,
.fc-color-picker > li .svg-inline--fa:hover,
.fc-color-picker > li .ion:hover {
  -webkit-transform: rotate(30deg);
  transform: rotate(30deg);
}

#add-new-event {
  transition: all linear .3s;
}

.external-event {
  box-shadow: 0 0 1px rgba(0, 0, 0, 0.125), 0 1px 3px rgba(0, 0, 0, 0.2);
  border-radius: 0.25rem;
  cursor: move;
  font-weight: 700;
  margin-bottom: 4px;
  padding: 5px 10px;
}

.external-event:hover {
  box-shadow: inset 0 0 90px rgba(0, 0, 0, 0.2);
}

.select2-container--default .select2-selection--single {
  border: 1px solid #ced4da;
  padding: 0.46875rem 0.75rem;
  height: calc(2.25rem + 2px);
}

.select2-container--default.select2-container--open .select2-selection--single {
  border-color: #80bdff;
}

.select2-container--default .select2-dropdown {
  border: 1px solid #ced4da;
}

.select2-container--default .select2-results__option {
  padding: 6px 12px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
  padding-left: 0;
  height: auto;
  margin-top: -3px;
}

.select2-container--default[dir="rtl"] .select2-selection--single .select2-selection__rendered {
  padding-right: 6px;
  padding-left: 20px;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
  height: 31px;
  right: 6px;
}

.select2-container--default .select2-selection--single .select2-selection__arrow b {
  margin-top: 0;
}

.select2-container--default .select2-dropdown .select2-search__field,
.select2-container--default .select2-search--inline .select2-search__field {
  border: 1px solid #ced4da;
}

.select2-container--default .select2-dropdown .select2-search__field:focus,
.select2-container--default .select2-search--inline .select2-search__field:focus {
  outline: none;
  border: 1px solid #80bdff;
}

.select2-container--default .select2-dropdown.select2-dropdown--below {
  border-top: 0;
}

.select2-container--default .select2-dropdown.select2-dropdown--above {
  border-bottom: 0;
}

.select2-container--default .select2-results__option[aria-disabled='true'] {
  color: #6c757d;
}

.select2-container--default .select2-results__option[aria-selected='true'] {
  background-color: #dee2e6;
}

.select2-container--default .select2-results__option[aria-selected='true'], .select2-container--default .select2-results__option[aria-selected='true']:hover {
  color: #1f2d3d;
}

.select2-container--default .select2-results__option--highlighted {
  background-color: #007bff;
  color: #fff;
}

.select2-container--default .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {
  background-color: #0074f0;
  color: #fff;
}

.select2-container--default .select2-selection--multiple {
  border: 1px solid #ced4da;
  min-height: calc(2.25rem + 2px);
}

.select2-container--default .select2-selection--multiple:focus {
  border-color: #80bdff;
}

.select2-container--default .select2-selection--multiple .select2-selection__rendered {
  padding: 0 0.375rem 0.375rem;
  margin-bottom: -0.375rem;
}

.select2-container--default .select2-selection--multiple .select2-selection__rendered li:first-child.select2-search.select2-search--inline {
  width: 100%;
  margin-left: 0.375rem;
}

.select2-container--default .select2-selection--multiple .select2-selection__rendered li:first-child.select2-search.select2-search--inline .select2-search__field {
  width: 100% !important;
}

.select2-container--default .select2-selection--multiple .select2-selection__rendered .select2-search.select2-search--inline .select2-search__field {
  border: 0;
  margin-top: 6px;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #007bff;
  border-color: #006fe6;
  color: #fff;
  padding: 0 10px;
  margin-top: .31rem;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  color: rgba(255, 255, 255, 0.7);
  float: right;
  margin-left: 5px;
  margin-right: -2px;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #fff;
}

.text-sm .select2-container--default .select2-selection--multiple .select2-search.select2-search--inline .select2-search__field, .select2-container--default .select2-selection--multiple.text-sm .select2-search.select2-search--inline .select2-search__field {
  margin-top: 8px;
}

.text-sm .select2-container--default .select2-selection--multiple .select2-selection__choice, .select2-container--default .select2-selection--multiple.text-sm .select2-selection__choice {
  margin-top: .4rem;
}

.select2-container--default.select2-container--focus .select2-selection--single,
.select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #80bdff;
}

.select2-container--default.select2-container--focus .select2-search__field {
  border: 0;
}

.select2-container--default .select2-selection--single .select2-selection__rendered li {
  padding-right: 10px;
}

.input-group-prepend ~ .select2-container--default .select2-selection {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
}

.input-group > .select2-container--default:not(:last-child) .select2-selection {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
}

.select2-container--bootstrap4.select2-container--focus .select2-selection {
  box-shadow: none;
}

select.form-control-sm ~ .select2-container--default {
  font-size: 75%;
}

.text-sm .select2-container--default .select2-selection--single,
select.form-control-sm ~ .select2-container--default .select2-selection--single {
  height: calc(1.8125rem + 2px);
}

.text-sm .select2-container--default .select2-selection--single .select2-selection__rendered,
select.form-control-sm ~ .select2-container--default .select2-selection--single .select2-selection__rendered {
  margin-top: -.4rem;
}

.text-sm .select2-container--default .select2-selection--single .select2-selection__arrow,
select.form-control-sm ~ .select2-container--default .select2-selection--single .select2-selection__arrow {
  top: -.12rem;
}

.text-sm .select2-container--default .select2-selection--multiple,
select.form-control-sm ~ .select2-container--default .select2-selection--multiple {
  min-height: calc(1.8125rem + 2px);
}

.text-sm .select2-container--default .select2-selection--multiple .select2-selection__rendered,
select.form-control-sm ~ .select2-container--default .select2-selection--multiple .select2-selection__rendered {
  padding: 0 0.25rem 0.25rem;
  margin-top: -0.1rem;
}

.text-sm .select2-container--default .select2-selection--multiple .select2-selection__rendered li:first-child.select2-search.select2-search--inline,
select.form-control-sm ~ .select2-container--default .select2-selection--multiple .select2-selection__rendered li:first-child.select2-search.select2-search--inline {
  margin-left: 0.25rem;
}

.text-sm .select2-container--default .select2-selection--multiple .select2-selection__rendered .select2-search.select2-search--inline .select2-search__field,
select.form-control-sm ~ .select2-container--default .select2-selection--multiple .select2-selection__rendered .select2-search.select2-search--inline .select2-search__field {
  margin-top: 6px;
}

.maximized-card .select2-dropdown {
  z-index: 9999;
}

.select2-primary + .select2-container--default.select2-container--open .select2-selection--single {
  border-color: #80bdff;
}

.select2-primary + .select2-container--default.select2-container--focus .select2-selection--single {
  border-color: #80bdff;
}

.select2-container--default .select2-primary.select2-dropdown .select2-search__field:focus,
.select2-container--default .select2-primary .select2-dropdown .select2-search__field:focus,
.select2-container--default .select2-primary .select2-search--inline .select2-search__field:focus,
.select2-primary .select2-container--default.select2-dropdown .select2-search__field:focus,
.select2-primary .select2-container--default .select2-dropdown .select2-search__field:focus,
.select2-primary .select2-container--default .select2-search--inline .select2-search__field:focus {
  border: 1px solid #80bdff;
}

.select2-container--default .select2-primary .select2-results__option--highlighted,
.select2-primary .select2-container--default .select2-results__option--highlighted {
  background-color: #007bff;
  color: #fff;
}

.select2-container--default .select2-primary .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-primary .select2-results__option--highlighted[aria-selected]:hover,
.select2-primary .select2-container--default .select2-results__option--highlighted[aria-selected],
.select2-primary .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {
  background-color: #0074f0;
  color: #fff;
}

.select2-container--default .select2-primary .select2-selection--multiple:focus,
.select2-primary .select2-container--default .select2-selection--multiple:focus {
  border-color: #80bdff;
}

.select2-container--default .select2-primary .select2-selection--multiple .select2-selection__choice,
.select2-primary .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #007bff;
  border-color: #006fe6;
  color: #fff;
}

.select2-container--default .select2-primary .select2-selection--multiple .select2-selection__choice__remove,
.select2-primary .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  color: rgba(255, 255, 255, 0.7);
}

.select2-container--default .select2-primary .select2-selection--multiple .select2-selection__choice__remove:hover,
.select2-primary .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #fff;
}

.select2-container--default .select2-primary.select2-container--focus .select2-selection--multiple,
.select2-primary .select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #80bdff;
}

.select2-secondary + .select2-container--default.select2-container--open .select2-selection--single {
  border-color: #afb5ba;
}

.select2-secondary + .select2-container--default.select2-container--focus .select2-selection--single {
  border-color: #afb5ba;
}

.select2-container--default .select2-secondary.select2-dropdown .select2-search__field:focus,
.select2-container--default .select2-secondary .select2-dropdown .select2-search__field:focus,
.select2-container--default .select2-secondary .select2-search--inline .select2-search__field:focus,
.select2-secondary .select2-container--default.select2-dropdown .select2-search__field:focus,
.select2-secondary .select2-container--default .select2-dropdown .select2-search__field:focus,
.select2-secondary .select2-container--default .select2-search--inline .select2-search__field:focus {
  border: 1px solid #afb5ba;
}

.select2-container--default .select2-secondary .select2-results__option--highlighted,
.select2-secondary .select2-container--default .select2-results__option--highlighted {
  background-color: #6c757d;
  color: #fff;
}

.select2-container--default .select2-secondary .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-secondary .select2-results__option--highlighted[aria-selected]:hover,
.select2-secondary .select2-container--default .select2-results__option--highlighted[aria-selected],
.select2-secondary .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {
  background-color: #656d75;
  color: #fff;
}

.select2-container--default .select2-secondary .select2-selection--multiple:focus,
.select2-secondary .select2-container--default .select2-selection--multiple:focus {
  border-color: #afb5ba;
}

.select2-container--default .select2-secondary .select2-selection--multiple .select2-selection__choice,
.select2-secondary .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #6c757d;
  border-color: #60686f;
  color: #fff;
}

.select2-container--default .select2-secondary .select2-selection--multiple .select2-selection__choice__remove,
.select2-secondary .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  color: rgba(255, 255, 255, 0.7);
}

.select2-container--default .select2-secondary .select2-selection--multiple .select2-selection__choice__remove:hover,
.select2-secondary .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #fff;
}

.select2-container--default .select2-secondary.select2-container--focus .select2-selection--multiple,
.select2-secondary .select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #afb5ba;
}

.select2-success + .select2-container--default.select2-container--open .select2-selection--single {
  border-color: #71dd8a;
}

.select2-success + .select2-container--default.select2-container--focus .select2-selection--single {
  border-color: #71dd8a;
}

.select2-container--default .select2-success.select2-dropdown .select2-search__field:focus,
.select2-container--default .select2-success .select2-dropdown .select2-search__field:focus,
.select2-container--default .select2-success .select2-search--inline .select2-search__field:focus,
.select2-success .select2-container--default.select2-dropdown .select2-search__field:focus,
.select2-success .select2-container--default .select2-dropdown .select2-search__field:focus,
.select2-success .select2-container--default .select2-search--inline .select2-search__field:focus {
  border: 1px solid #71dd8a;
}

.select2-container--default .select2-success .select2-results__option--highlighted,
.select2-success .select2-container--default .select2-results__option--highlighted {
  background-color: #28a745;
  color: #fff;
}

.select2-container--default .select2-success .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-success .select2-results__option--highlighted[aria-selected]:hover,
.select2-success .select2-container--default .select2-results__option--highlighted[aria-selected],
.select2-success .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {
  background-color: #259b40;
  color: #fff;
}

.select2-container--default .select2-success .select2-selection--multiple:focus,
.select2-success .select2-container--default .select2-selection--multiple:focus {
  border-color: #71dd8a;
}

.select2-container--default .select2-success .select2-selection--multiple .select2-selection__choice,
.select2-success .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #28a745;
  border-color: #23923d;
  color: #fff;
}

.select2-container--default .select2-success .select2-selection--multiple .select2-selection__choice__remove,
.select2-success .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  color: rgba(255, 255, 255, 0.7);
}

.select2-container--default .select2-success .select2-selection--multiple .select2-selection__choice__remove:hover,
.select2-success .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #fff;
}

.select2-container--default .select2-success.select2-container--focus .select2-selection--multiple,
.select2-success .select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #71dd8a;
}

.select2-info + .select2-container--default.select2-container--open .select2-selection--single {
  border-color: #63d9ec;
}

.select2-info + .select2-container--default.select2-container--focus .select2-selection--single {
  border-color: #63d9ec;
}

.select2-container--default .select2-info.select2-dropdown .select2-search__field:focus,
.select2-container--default .select2-info .select2-dropdown .select2-search__field:focus,
.select2-container--default .select2-info .select2-search--inline .select2-search__field:focus,
.select2-info .select2-container--default.select2-dropdown .select2-search__field:focus,
.select2-info .select2-container--default .select2-dropdown .select2-search__field:focus,
.select2-info .select2-container--default .select2-search--inline .select2-search__field:focus {
  border: 1px solid #63d9ec;
}

.select2-container--default .select2-info .select2-results__option--highlighted,
.select2-info .select2-container--default .select2-results__option--highlighted {
  background-color: #17a2b8;
  color: #fff;
}

.select2-container--default .select2-info .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-info .select2-results__option--highlighted[aria-selected]:hover,
.select2-info .select2-container--default .select2-results__option--highlighted[aria-selected],
.select2-info .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {
  background-color: #1596aa;
  color: #fff;
}

.select2-container--default .select2-info .select2-selection--multiple:focus,
.select2-info .select2-container--default .select2-selection--multiple:focus {
  border-color: #63d9ec;
}

.select2-container--default .select2-info .select2-selection--multiple .select2-selection__choice,
.select2-info .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #17a2b8;
  border-color: #148ea1;
  color: #fff;
}

.select2-container--default .select2-info .select2-selection--multiple .select2-selection__choice__remove,
.select2-info .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  color: rgba(255, 255, 255, 0.7);
}

.select2-container--default .select2-info .select2-selection--multiple .select2-selection__choice__remove:hover,
.select2-info .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #fff;
}

.select2-container--default .select2-info.select2-container--focus .select2-selection--multiple,
.select2-info .select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #63d9ec;
}

.select2-warning + .select2-container--default.select2-container--open .select2-selection--single {
  border-color: #ffe187;
}

.select2-warning + .select2-container--default.select2-container--focus .select2-selection--single {
  border-color: #ffe187;
}

.select2-container--default .select2-warning.select2-dropdown .select2-search__field:focus,
.select2-container--default .select2-warning .select2-dropdown .select2-search__field:focus,
.select2-container--default .select2-warning .select2-search--inline .select2-search__field:focus,
.select2-warning .select2-container--default.select2-dropdown .select2-search__field:focus,
.select2-warning .select2-container--default .select2-dropdown .select2-search__field:focus,
.select2-warning .select2-container--default .select2-search--inline .select2-search__field:focus {
  border: 1px solid #ffe187;
}

.select2-container--default .select2-warning .select2-results__option--highlighted,
.select2-warning .select2-container--default .select2-results__option--highlighted {
  background-color: #ffc107;
  color: #1f2d3d;
}

.select2-container--default .select2-warning .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-warning .select2-results__option--highlighted[aria-selected]:hover,
.select2-warning .select2-container--default .select2-results__option--highlighted[aria-selected],
.select2-warning .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {
  background-color: #f7b900;
  color: #1f2d3d;
}

.select2-container--default .select2-warning .select2-selection--multiple:focus,
.select2-warning .select2-container--default .select2-selection--multiple:focus {
  border-color: #ffe187;
}

.select2-container--default .select2-warning .select2-selection--multiple .select2-selection__choice,
.select2-warning .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #ffc107;
  border-color: #edb100;
  color: #1f2d3d;
}

.select2-container--default .select2-warning .select2-selection--multiple .select2-selection__choice__remove,
.select2-warning .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  color: rgba(31, 45, 61, 0.7);
}

.select2-container--default .select2-warning .select2-selection--multiple .select2-selection__choice__remove:hover,
.select2-warning .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #1f2d3d;
}

.select2-container--default .select2-warning.select2-container--focus .select2-selection--multiple,
.select2-warning .select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #ffe187;
}

.select2-danger + .select2-container--default.select2-container--open .select2-selection--single {
  border-color: #efa2a9;
}

.select2-danger + .select2-container--default.select2-container--focus .select2-selection--single {
  border-color: #efa2a9;
}

.select2-container--default .select2-danger.select2-dropdown .select2-search__field:focus,
.select2-container--default .select2-danger .select2-dropdown .select2-search__field:focus,
.select2-container--default .select2-danger .select2-search--inline .select2-search__field:focus,
.select2-danger .select2-container--default.select2-dropdown .select2-search__field:focus,
.select2-danger .select2-container--default .select2-dropdown .select2-search__field:focus,
.select2-danger .select2-container--default .select2-search--inline .select2-search__field:focus {
  border: 1px solid #efa2a9;
}

.select2-container--default .select2-danger .select2-results__option--highlighted,
.select2-danger .select2-container--default .select2-results__option--highlighted {
  background-color: #dc3545;
  color: #fff;
}

.select2-container--default .select2-danger .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-danger .select2-results__option--highlighted[aria-selected]:hover,
.select2-danger .select2-container--default .select2-results__option--highlighted[aria-selected],
.select2-danger .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {
  background-color: #da2839;
  color: #fff;
}

.select2-container--default .select2-danger .select2-selection--multiple:focus,
.select2-danger .select2-container--default .select2-selection--multiple:focus {
  border-color: #efa2a9;
}

.select2-container--default .select2-danger .select2-selection--multiple .select2-selection__choice,
.select2-danger .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #dc3545;
  border-color: #d32535;
  color: #fff;
}

.select2-container--default .select2-danger .select2-selection--multiple .select2-selection__choice__remove,
.select2-danger .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  color: rgba(255, 255, 255, 0.7);
}

.select2-container--default .select2-danger .select2-selection--multiple .select2-selection__choice__remove:hover,
.select2-danger .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #fff;
}

.select2-container--default .select2-danger.select2-container--focus .select2-selection--multiple,
.select2-danger .select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #efa2a9;
}

.select2-light + .select2-container--default.select2-container--open .select2-selection--single {
  border-color: white;
}

.select2-light + .select2-container--default.select2-container--focus .select2-selection--single {
  border-color: white;
}

.select2-container--default .select2-light.select2-dropdown .select2-search__field:focus,
.select2-container--default .select2-light .select2-dropdown .select2-search__field:focus,
.select2-container--default .select2-light .select2-search--inline .select2-search__field:focus,
.select2-light .select2-container--default.select2-dropdown .select2-search__field:focus,
.select2-light .select2-container--default .select2-dropdown .select2-search__field:focus,
.select2-light .select2-container--default .select2-search--inline .select2-search__field:focus {
  border: 1px solid white;
}

.select2-container--default .select2-light .select2-results__option--highlighted,
.select2-light .select2-container--default .select2-results__option--highlighted {
  background-color: #f8f9fa;
  color: #1f2d3d;
}

.select2-container--default .select2-light .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-light .select2-results__option--highlighted[aria-selected]:hover,
.select2-light .select2-container--default .select2-results__option--highlighted[aria-selected],
.select2-light .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {
  background-color: #eff1f4;
  color: #1f2d3d;
}

.select2-container--default .select2-light .select2-selection--multiple:focus,
.select2-light .select2-container--default .select2-selection--multiple:focus {
  border-color: white;
}

.select2-container--default .select2-light .select2-selection--multiple .select2-selection__choice,
.select2-light .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #f8f9fa;
  border-color: #e9ecef;
  color: #1f2d3d;
}

.select2-container--default .select2-light .select2-selection--multiple .select2-selection__choice__remove,
.select2-light .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  color: rgba(31, 45, 61, 0.7);
}

.select2-container--default .select2-light .select2-selection--multiple .select2-selection__choice__remove:hover,
.select2-light .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #1f2d3d;
}

.select2-container--default .select2-light.select2-container--focus .select2-selection--multiple,
.select2-light .select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: white;
}

.select2-dark + .select2-container--default.select2-container--open .select2-selection--single {
  border-color: #6d7a86;
}

.select2-dark + .select2-container--default.select2-container--focus .select2-selection--single {
  border-color: #6d7a86;
}

.select2-container--default .select2-dark.select2-dropdown .select2-search__field:focus,
.select2-container--default .select2-dark .select2-dropdown .select2-search__field:focus,
.select2-container--default .select2-dark .select2-search--inline .select2-search__field:focus,
.select2-dark .select2-container--default.select2-dropdown .select2-search__field:focus,
.select2-dark .select2-container--default .select2-dropdown .select2-search__field:focus,
.select2-dark .select2-container--default .select2-search--inline .select2-search__field:focus {
  border: 1px solid #6d7a86;
}

.select2-container--default .select2-dark .select2-results__option--highlighted,
.select2-dark .select2-container--default .select2-results__option--highlighted {
  background-color: #343a40;
  color: #fff;
}

.select2-container--default .select2-dark .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-dark .select2-results__option--highlighted[aria-selected]:hover,
.select2-dark .select2-container--default .select2-results__option--highlighted[aria-selected],
.select2-dark .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {
  background-color: #2d3238;
  color: #fff;
}

.select2-container--default .select2-dark .select2-selection--multiple:focus,
.select2-dark .select2-container--default .select2-selection--multiple:focus {
  border-color: #6d7a86;
}

.select2-container--default .select2-dark .select2-selection--multiple .select2-selection__choice,
.select2-dark .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #343a40;
  border-color: #292d32;
  color: #fff;
}

.select2-container--default .select2-dark .select2-selection--multiple .select2-selection__choice__remove,
.select2-dark .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  color: rgba(255, 255, 255, 0.7);
}

.select2-container--default .select2-dark .select2-selection--multiple .select2-selection__choice__remove:hover,
.select2-dark .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #fff;
}

.select2-container--default .select2-dark.select2-container--focus .select2-selection--multiple,
.select2-dark .select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #6d7a86;
}

.select2-lightblue + .select2-container--default.select2-container--open .select2-selection--single {
  border-color: #99c5de;
}

.select2-lightblue + .select2-container--default.select2-container--focus .select2-selection--single {
  border-color: #99c5de;
}

.select2-container--default .select2-lightblue.select2-dropdown .select2-search__field:focus,
.select2-container--default .select2-lightblue .select2-dropdown .select2-search__field:focus,
.select2-container--default .select2-lightblue .select2-search--inline .select2-search__field:focus,
.select2-lightblue .select2-container--default.select2-dropdown .select2-search__field:focus,
.select2-lightblue .select2-container--default .select2-dropdown .select2-search__field:focus,
.select2-lightblue .select2-container--default .select2-search--inline .select2-search__field:focus {
  border: 1px solid #99c5de;
}

.select2-container--default .select2-lightblue .select2-results__option--highlighted,
.select2-lightblue .select2-container--default .select2-results__option--highlighted {
  background-color: #3c8dbc;
  color: #fff;
}

.select2-container--default .select2-lightblue .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-lightblue .select2-results__option--highlighted[aria-selected]:hover,
.select2-lightblue .select2-container--default .select2-results__option--highlighted[aria-selected],
.select2-lightblue .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {
  background-color: #3884b0;
  color: #fff;
}

.select2-container--default .select2-lightblue .select2-selection--multiple:focus,
.select2-lightblue .select2-container--default .select2-selection--multiple:focus {
  border-color: #99c5de;
}

.select2-container--default .select2-lightblue .select2-selection--multiple .select2-selection__choice,
.select2-lightblue .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #3c8dbc;
  border-color: #367fa9;
  color: #fff;
}

.select2-container--default .select2-lightblue .select2-selection--multiple .select2-selection__choice__remove,
.select2-lightblue .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  color: rgba(255, 255, 255, 0.7);
}

.select2-container--default .select2-lightblue .select2-selection--multiple .select2-selection__choice__remove:hover,
.select2-lightblue .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #fff;
}

.select2-container--default .select2-lightblue.select2-container--focus .select2-selection--multiple,
.select2-lightblue .select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #99c5de;
}

.select2-navy + .select2-container--default.select2-container--open .select2-selection--single {
  border-color: #005ebf;
}

.select2-navy + .select2-container--default.select2-container--focus .select2-selection--single {
  border-color: #005ebf;
}

.select2-container--default .select2-navy.select2-dropdown .select2-search__field:focus,
.select2-container--default .select2-navy .select2-dropdown .select2-search__field:focus,
.select2-container--default .select2-navy .select2-search--inline .select2-search__field:focus,
.select2-navy .select2-container--default.select2-dropdown .select2-search__field:focus,
.select2-navy .select2-container--default .select2-dropdown .select2-search__field:focus,
.select2-navy .select2-container--default .select2-search--inline .select2-search__field:focus {
  border: 1px solid #005ebf;
}

.select2-container--default .select2-navy .select2-results__option--highlighted,
.select2-navy .select2-container--default .select2-results__option--highlighted {
  background-color: #001f3f;
  color: #fff;
}

.select2-container--default .select2-navy .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-navy .select2-results__option--highlighted[aria-selected]:hover,
.select2-navy .select2-container--default .select2-results__option--highlighted[aria-selected],
.select2-navy .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {
  background-color: #001730;
  color: #fff;
}

.select2-container--default .select2-navy .select2-selection--multiple:focus,
.select2-navy .select2-container--default .select2-selection--multiple:focus {
  border-color: #005ebf;
}

.select2-container--default .select2-navy .select2-selection--multiple .select2-selection__choice,
.select2-navy .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #001f3f;
  border-color: #001226;
  color: #fff;
}

.select2-container--default .select2-navy .select2-selection--multiple .select2-selection__choice__remove,
.select2-navy .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  color: rgba(255, 255, 255, 0.7);
}

.select2-container--default .select2-navy .select2-selection--multiple .select2-selection__choice__remove:hover,
.select2-navy .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #fff;
}

.select2-container--default .select2-navy.select2-container--focus .select2-selection--multiple,
.select2-navy .select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #005ebf;
}

.select2-olive + .select2-container--default.select2-container--open .select2-selection--single {
  border-color: #87cfaf;
}

.select2-olive + .select2-container--default.select2-container--focus .select2-selection--single {
  border-color: #87cfaf;
}

.select2-container--default .select2-olive.select2-dropdown .select2-search__field:focus,
.select2-container--default .select2-olive .select2-dropdown .select2-search__field:focus,
.select2-container--default .select2-olive .select2-search--inline .select2-search__field:focus,
.select2-olive .select2-container--default.select2-dropdown .select2-search__field:focus,
.select2-olive .select2-container--default .select2-dropdown .select2-search__field:focus,
.select2-olive .select2-container--default .select2-search--inline .select2-search__field:focus {
  border: 1px solid #87cfaf;
}

.select2-container--default .select2-olive .select2-results__option--highlighted,
.select2-olive .select2-container--default .select2-results__option--highlighted {
  background-color: #3d9970;
  color: #fff;
}

.select2-container--default .select2-olive .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-olive .select2-results__option--highlighted[aria-selected]:hover,
.select2-olive .select2-container--default .select2-results__option--highlighted[aria-selected],
.select2-olive .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {
  background-color: #398e68;
  color: #fff;
}

.select2-container--default .select2-olive .select2-selection--multiple:focus,
.select2-olive .select2-container--default .select2-selection--multiple:focus {
  border-color: #87cfaf;
}

.select2-container--default .select2-olive .select2-selection--multiple .select2-selection__choice,
.select2-olive .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #3d9970;
  border-color: #368763;
  color: #fff;
}

.select2-container--default .select2-olive .select2-selection--multiple .select2-selection__choice__remove,
.select2-olive .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  color: rgba(255, 255, 255, 0.7);
}

.select2-container--default .select2-olive .select2-selection--multiple .select2-selection__choice__remove:hover,
.select2-olive .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #fff;
}

.select2-container--default .select2-olive.select2-container--focus .select2-selection--multiple,
.select2-olive .select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #87cfaf;
}

.select2-lime + .select2-container--default.select2-container--open .select2-selection--single {
  border-color: #81ffb8;
}

.select2-lime + .select2-container--default.select2-container--focus .select2-selection--single {
  border-color: #81ffb8;
}

.select2-container--default .select2-lime.select2-dropdown .select2-search__field:focus,
.select2-container--default .select2-lime .select2-dropdown .select2-search__field:focus,
.select2-container--default .select2-lime .select2-search--inline .select2-search__field:focus,
.select2-lime .select2-container--default.select2-dropdown .select2-search__field:focus,
.select2-lime .select2-container--default .select2-dropdown .select2-search__field:focus,
.select2-lime .select2-container--default .select2-search--inline .select2-search__field:focus {
  border: 1px solid #81ffb8;
}

.select2-container--default .select2-lime .select2-results__option--highlighted,
.select2-lime .select2-container--default .select2-results__option--highlighted {
  background-color: #01ff70;
  color: #1f2d3d;
}

.select2-container--default .select2-lime .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-lime .select2-results__option--highlighted[aria-selected]:hover,
.select2-lime .select2-container--default .select2-results__option--highlighted[aria-selected],
.select2-lime .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {
  background-color: #00f169;
  color: #1f2d3d;
}

.select2-container--default .select2-lime .select2-selection--multiple:focus,
.select2-lime .select2-container--default .select2-selection--multiple:focus {
  border-color: #81ffb8;
}

.select2-container--default .select2-lime .select2-selection--multiple .select2-selection__choice,
.select2-lime .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #01ff70;
  border-color: #00e765;
  color: #1f2d3d;
}

.select2-container--default .select2-lime .select2-selection--multiple .select2-selection__choice__remove,
.select2-lime .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  color: rgba(31, 45, 61, 0.7);
}

.select2-container--default .select2-lime .select2-selection--multiple .select2-selection__choice__remove:hover,
.select2-lime .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #1f2d3d;
}

.select2-container--default .select2-lime.select2-container--focus .select2-selection--multiple,
.select2-lime .select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #81ffb8;
}

.select2-fuchsia + .select2-container--default.select2-container--open .select2-selection--single {
  border-color: #f88adf;
}

.select2-fuchsia + .select2-container--default.select2-container--focus .select2-selection--single {
  border-color: #f88adf;
}

.select2-container--default .select2-fuchsia.select2-dropdown .select2-search__field:focus,
.select2-container--default .select2-fuchsia .select2-dropdown .select2-search__field:focus,
.select2-container--default .select2-fuchsia .select2-search--inline .select2-search__field:focus,
.select2-fuchsia .select2-container--default.select2-dropdown .select2-search__field:focus,
.select2-fuchsia .select2-container--default .select2-dropdown .select2-search__field:focus,
.select2-fuchsia .select2-container--default .select2-search--inline .select2-search__field:focus {
  border: 1px solid #f88adf;
}

.select2-container--default .select2-fuchsia .select2-results__option--highlighted,
.select2-fuchsia .select2-container--default .select2-results__option--highlighted {
  background-color: #f012be;
  color: #fff;
}

.select2-container--default .select2-fuchsia .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-fuchsia .select2-results__option--highlighted[aria-selected]:hover,
.select2-fuchsia .select2-container--default .select2-results__option--highlighted[aria-selected],
.select2-fuchsia .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {
  background-color: #e40eb4;
  color: #fff;
}

.select2-container--default .select2-fuchsia .select2-selection--multiple:focus,
.select2-fuchsia .select2-container--default .select2-selection--multiple:focus {
  border-color: #f88adf;
}

.select2-container--default .select2-fuchsia .select2-selection--multiple .select2-selection__choice,
.select2-fuchsia .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #f012be;
  border-color: #db0ead;
  color: #fff;
}

.select2-container--default .select2-fuchsia .select2-selection--multiple .select2-selection__choice__remove,
.select2-fuchsia .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  color: rgba(255, 255, 255, 0.7);
}

.select2-container--default .select2-fuchsia .select2-selection--multiple .select2-selection__choice__remove:hover,
.select2-fuchsia .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #fff;
}

.select2-container--default .select2-fuchsia.select2-container--focus .select2-selection--multiple,
.select2-fuchsia .select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #f88adf;
}

.select2-maroon + .select2-container--default.select2-container--open .select2-selection--single {
  border-color: #f083ab;
}

.select2-maroon + .select2-container--default.select2-container--focus .select2-selection--single {
  border-color: #f083ab;
}

.select2-container--default .select2-maroon.select2-dropdown .select2-search__field:focus,
.select2-container--default .select2-maroon .select2-dropdown .select2-search__field:focus,
.select2-container--default .select2-maroon .select2-search--inline .select2-search__field:focus,
.select2-maroon .select2-container--default.select2-dropdown .select2-search__field:focus,
.select2-maroon .select2-container--default .select2-dropdown .select2-search__field:focus,
.select2-maroon .select2-container--default .select2-search--inline .select2-search__field:focus {
  border: 1px solid #f083ab;
}

.select2-container--default .select2-maroon .select2-results__option--highlighted,
.select2-maroon .select2-container--default .select2-results__option--highlighted {
  background-color: #d81b60;
  color: #fff;
}

.select2-container--default .select2-maroon .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-maroon .select2-results__option--highlighted[aria-selected]:hover,
.select2-maroon .select2-container--default .select2-results__option--highlighted[aria-selected],
.select2-maroon .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {
  background-color: #ca195a;
  color: #fff;
}

.select2-container--default .select2-maroon .select2-selection--multiple:focus,
.select2-maroon .select2-container--default .select2-selection--multiple:focus {
  border-color: #f083ab;
}

.select2-container--default .select2-maroon .select2-selection--multiple .select2-selection__choice,
.select2-maroon .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #d81b60;
  border-color: #c11856;
  color: #fff;
}

.select2-container--default .select2-maroon .select2-selection--multiple .select2-selection__choice__remove,
.select2-maroon .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  color: rgba(255, 255, 255, 0.7);
}

.select2-container--default .select2-maroon .select2-selection--multiple .select2-selection__choice__remove:hover,
.select2-maroon .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #fff;
}

.select2-container--default .select2-maroon.select2-container--focus .select2-selection--multiple,
.select2-maroon .select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #f083ab;
}

.select2-blue + .select2-container--default.select2-container--open .select2-selection--single {
  border-color: #80bdff;
}

.select2-blue + .select2-container--default.select2-container--focus .select2-selection--single {
  border-color: #80bdff;
}

.select2-container--default .select2-blue.select2-dropdown .select2-search__field:focus,
.select2-container--default .select2-blue .select2-dropdown .select2-search__field:focus,
.select2-container--default .select2-blue .select2-search--inline .select2-search__field:focus,
.select2-blue .select2-container--default.select2-dropdown .select2-search__field:focus,
.select2-blue .select2-container--default .select2-dropdown .select2-search__field:focus,
.select2-blue .select2-container--default .select2-search--inline .select2-search__field:focus {
  border: 1px solid #80bdff;
}

.select2-container--default .select2-blue .select2-results__option--highlighted,
.select2-blue .select2-container--default .select2-results__option--highlighted {
  background-color: #007bff;
  color: #fff;
}

.select2-container--default .select2-blue .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-blue .select2-results__option--highlighted[aria-selected]:hover,
.select2-blue .select2-container--default .select2-results__option--highlighted[aria-selected],
.select2-blue .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {
  background-color: #0074f0;
  color: #fff;
}

.select2-container--default .select2-blue .select2-selection--multiple:focus,
.select2-blue .select2-container--default .select2-selection--multiple:focus {
  border-color: #80bdff;
}

.select2-container--default .select2-blue .select2-selection--multiple .select2-selection__choice,
.select2-blue .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #007bff;
  border-color: #006fe6;
  color: #fff;
}

.select2-container--default .select2-blue .select2-selection--multiple .select2-selection__choice__remove,
.select2-blue .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  color: rgba(255, 255, 255, 0.7);
}

.select2-container--default .select2-blue .select2-selection--multiple .select2-selection__choice__remove:hover,
.select2-blue .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #fff;
}

.select2-container--default .select2-blue.select2-container--focus .select2-selection--multiple,
.select2-blue .select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #80bdff;
}

.select2-indigo + .select2-container--default.select2-container--open .select2-selection--single {
  border-color: #b389f9;
}

.select2-indigo + .select2-container--default.select2-container--focus .select2-selection--single {
  border-color: #b389f9;
}

.select2-container--default .select2-indigo.select2-dropdown .select2-search__field:focus,
.select2-container--default .select2-indigo .select2-dropdown .select2-search__field:focus,
.select2-container--default .select2-indigo .select2-search--inline .select2-search__field:focus,
.select2-indigo .select2-container--default.select2-dropdown .select2-search__field:focus,
.select2-indigo .select2-container--default .select2-dropdown .select2-search__field:focus,
.select2-indigo .select2-container--default .select2-search--inline .select2-search__field:focus {
  border: 1px solid #b389f9;
}

.select2-container--default .select2-indigo .select2-results__option--highlighted,
.select2-indigo .select2-container--default .select2-results__option--highlighted {
  background-color: #6610f2;
  color: #fff;
}

.select2-container--default .select2-indigo .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-indigo .select2-results__option--highlighted[aria-selected]:hover,
.select2-indigo .select2-container--default .select2-results__option--highlighted[aria-selected],
.select2-indigo .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {
  background-color: #5f0de6;
  color: #fff;
}

.select2-container--default .select2-indigo .select2-selection--multiple:focus,
.select2-indigo .select2-container--default .select2-selection--multiple:focus {
  border-color: #b389f9;
}

.select2-container--default .select2-indigo .select2-selection--multiple .select2-selection__choice,
.select2-indigo .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #6610f2;
  border-color: #5b0cdd;
  color: #fff;
}

.select2-container--default .select2-indigo .select2-selection--multiple .select2-selection__choice__remove,
.select2-indigo .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  color: rgba(255, 255, 255, 0.7);
}

.select2-container--default .select2-indigo .select2-selection--multiple .select2-selection__choice__remove:hover,
.select2-indigo .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #fff;
}

.select2-container--default .select2-indigo.select2-container--focus .select2-selection--multiple,
.select2-indigo .select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #b389f9;
}

.select2-purple + .select2-container--default.select2-container--open .select2-selection--single {
  border-color: #b8a2e0;
}

.select2-purple + .select2-container--default.select2-container--focus .select2-selection--single {
  border-color: #b8a2e0;
}

.select2-container--default .select2-purple.select2-dropdown .select2-search__field:focus,
.select2-container--default .select2-purple .select2-dropdown .select2-search__field:focus,
.select2-container--default .select2-purple .select2-search--inline .select2-search__field:focus,
.select2-purple .select2-container--default.select2-dropdown .select2-search__field:focus,
.select2-purple .select2-container--default .select2-dropdown .select2-search__field:focus,
.select2-purple .select2-container--default .select2-search--inline .select2-search__field:focus {
  border: 1px solid #b8a2e0;
}

.select2-container--default .select2-purple .select2-results__option--highlighted,
.select2-purple .select2-container--default .select2-results__option--highlighted {
  background-color: #6f42c1;
  color: #fff;
}

.select2-container--default .select2-purple .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-purple .select2-results__option--highlighted[aria-selected]:hover,
.select2-purple .select2-container--default .select2-results__option--highlighted[aria-selected],
.select2-purple .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {
  background-color: #683cb8;
  color: #fff;
}

.select2-container--default .select2-purple .select2-selection--multiple:focus,
.select2-purple .select2-container--default .select2-selection--multiple:focus {
  border-color: #b8a2e0;
}

.select2-container--default .select2-purple .select2-selection--multiple .select2-selection__choice,
.select2-purple .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #6f42c1;
  border-color: #643ab0;
  color: #fff;
}

.select2-container--default .select2-purple .select2-selection--multiple .select2-selection__choice__remove,
.select2-purple .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  color: rgba(255, 255, 255, 0.7);
}

.select2-container--default .select2-purple .select2-selection--multiple .select2-selection__choice__remove:hover,
.select2-purple .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #fff;
}

.select2-container--default .select2-purple.select2-container--focus .select2-selection--multiple,
.select2-purple .select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #b8a2e0;
}

.select2-pink + .select2-container--default.select2-container--open .select2-selection--single {
  border-color: #f6b0d0;
}

.select2-pink + .select2-container--default.select2-container--focus .select2-selection--single {
  border-color: #f6b0d0;
}

.select2-container--default .select2-pink.select2-dropdown .select2-search__field:focus,
.select2-container--default .select2-pink .select2-dropdown .select2-search__field:focus,
.select2-container--default .select2-pink .select2-search--inline .select2-search__field:focus,
.select2-pink .select2-container--default.select2-dropdown .select2-search__field:focus,
.select2-pink .select2-container--default .select2-dropdown .select2-search__field:focus,
.select2-pink .select2-container--default .select2-search--inline .select2-search__field:focus {
  border: 1px solid #f6b0d0;
}

.select2-container--default .select2-pink .select2-results__option--highlighted,
.select2-pink .select2-container--default .select2-results__option--highlighted {
  background-color: #e83e8c;
  color: #fff;
}

.select2-container--default .select2-pink .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-pink .select2-results__option--highlighted[aria-selected]:hover,
.select2-pink .select2-container--default .select2-results__option--highlighted[aria-selected],
.select2-pink .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {
  background-color: #e63084;
  color: #fff;
}

.select2-container--default .select2-pink .select2-selection--multiple:focus,
.select2-pink .select2-container--default .select2-selection--multiple:focus {
  border-color: #f6b0d0;
}

.select2-container--default .select2-pink .select2-selection--multiple .select2-selection__choice,
.select2-pink .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #e83e8c;
  border-color: #e5277e;
  color: #fff;
}

.select2-container--default .select2-pink .select2-selection--multiple .select2-selection__choice__remove,
.select2-pink .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  color: rgba(255, 255, 255, 0.7);
}

.select2-container--default .select2-pink .select2-selection--multiple .select2-selection__choice__remove:hover,
.select2-pink .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #fff;
}

.select2-container--default .select2-pink.select2-container--focus .select2-selection--multiple,
.select2-pink .select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #f6b0d0;
}

.select2-red + .select2-container--default.select2-container--open .select2-selection--single {
  border-color: #efa2a9;
}

.select2-red + .select2-container--default.select2-container--focus .select2-selection--single {
  border-color: #efa2a9;
}

.select2-container--default .select2-red.select2-dropdown .select2-search__field:focus,
.select2-container--default .select2-red .select2-dropdown .select2-search__field:focus,
.select2-container--default .select2-red .select2-search--inline .select2-search__field:focus,
.select2-red .select2-container--default.select2-dropdown .select2-search__field:focus,
.select2-red .select2-container--default .select2-dropdown .select2-search__field:focus,
.select2-red .select2-container--default .select2-search--inline .select2-search__field:focus {
  border: 1px solid #efa2a9;
}

.select2-container--default .select2-red .select2-results__option--highlighted,
.select2-red .select2-container--default .select2-results__option--highlighted {
  background-color: #dc3545;
  color: #fff;
}

.select2-container--default .select2-red .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-red .select2-results__option--highlighted[aria-selected]:hover,
.select2-red .select2-container--default .select2-results__option--highlighted[aria-selected],
.select2-red .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {
  background-color: #da2839;
  color: #fff;
}

.select2-container--default .select2-red .select2-selection--multiple:focus,
.select2-red .select2-container--default .select2-selection--multiple:focus {
  border-color: #efa2a9;
}

.select2-container--default .select2-red .select2-selection--multiple .select2-selection__choice,
.select2-red .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #dc3545;
  border-color: #d32535;
  color: #fff;
}

.select2-container--default .select2-red .select2-selection--multiple .select2-selection__choice__remove,
.select2-red .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  color: rgba(255, 255, 255, 0.7);
}

.select2-container--default .select2-red .select2-selection--multiple .select2-selection__choice__remove:hover,
.select2-red .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #fff;
}

.select2-container--default .select2-red.select2-container--focus .select2-selection--multiple,
.select2-red .select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #efa2a9;
}

.select2-orange + .select2-container--default.select2-container--open .select2-selection--single {
  border-color: #fec392;
}

.select2-orange + .select2-container--default.select2-container--focus .select2-selection--single {
  border-color: #fec392;
}

.select2-container--default .select2-orange.select2-dropdown .select2-search__field:focus,
.select2-container--default .select2-orange .select2-dropdown .select2-search__field:focus,
.select2-container--default .select2-orange .select2-search--inline .select2-search__field:focus,
.select2-orange .select2-container--default.select2-dropdown .select2-search__field:focus,
.select2-orange .select2-container--default .select2-dropdown .select2-search__field:focus,
.select2-orange .select2-container--default .select2-search--inline .select2-search__field:focus {
  border: 1px solid #fec392;
}

.select2-container--default .select2-orange .select2-results__option--highlighted,
.select2-orange .select2-container--default .select2-results__option--highlighted {
  background-color: #fd7e14;
  color: #1f2d3d;
}

.select2-container--default .select2-orange .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-orange .select2-results__option--highlighted[aria-selected]:hover,
.select2-orange .select2-container--default .select2-results__option--highlighted[aria-selected],
.select2-orange .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {
  background-color: #fd7605;
  color: #fff;
}

.select2-container--default .select2-orange .select2-selection--multiple:focus,
.select2-orange .select2-container--default .select2-selection--multiple:focus {
  border-color: #fec392;
}

.select2-container--default .select2-orange .select2-selection--multiple .select2-selection__choice,
.select2-orange .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #fd7e14;
  border-color: #f57102;
  color: #1f2d3d;
}

.select2-container--default .select2-orange .select2-selection--multiple .select2-selection__choice__remove,
.select2-orange .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  color: rgba(31, 45, 61, 0.7);
}

.select2-container--default .select2-orange .select2-selection--multiple .select2-selection__choice__remove:hover,
.select2-orange .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #1f2d3d;
}

.select2-container--default .select2-orange.select2-container--focus .select2-selection--multiple,
.select2-orange .select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #fec392;
}

.select2-yellow + .select2-container--default.select2-container--open .select2-selection--single {
  border-color: #ffe187;
}

.select2-yellow + .select2-container--default.select2-container--focus .select2-selection--single {
  border-color: #ffe187;
}

.select2-container--default .select2-yellow.select2-dropdown .select2-search__field:focus,
.select2-container--default .select2-yellow .select2-dropdown .select2-search__field:focus,
.select2-container--default .select2-yellow .select2-search--inline .select2-search__field:focus,
.select2-yellow .select2-container--default.select2-dropdown .select2-search__field:focus,
.select2-yellow .select2-container--default .select2-dropdown .select2-search__field:focus,
.select2-yellow .select2-container--default .select2-search--inline .select2-search__field:focus {
  border: 1px solid #ffe187;
}

.select2-container--default .select2-yellow .select2-results__option--highlighted,
.select2-yellow .select2-container--default .select2-results__option--highlighted {
  background-color: #ffc107;
  color: #1f2d3d;
}

.select2-container--default .select2-yellow .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-yellow .select2-results__option--highlighted[aria-selected]:hover,
.select2-yellow .select2-container--default .select2-results__option--highlighted[aria-selected],
.select2-yellow .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {
  background-color: #f7b900;
  color: #1f2d3d;
}

.select2-container--default .select2-yellow .select2-selection--multiple:focus,
.select2-yellow .select2-container--default .select2-selection--multiple:focus {
  border-color: #ffe187;
}

.select2-container--default .select2-yellow .select2-selection--multiple .select2-selection__choice,
.select2-yellow .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #ffc107;
  border-color: #edb100;
  color: #1f2d3d;
}

.select2-container--default .select2-yellow .select2-selection--multiple .select2-selection__choice__remove,
.select2-yellow .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  color: rgba(31, 45, 61, 0.7);
}

.select2-container--default .select2-yellow .select2-selection--multiple .select2-selection__choice__remove:hover,
.select2-yellow .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #1f2d3d;
}

.select2-container--default .select2-yellow.select2-container--focus .select2-selection--multiple,
.select2-yellow .select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #ffe187;
}

.select2-green + .select2-container--default.select2-container--open .select2-selection--single {
  border-color: #71dd8a;
}

.select2-green + .select2-container--default.select2-container--focus .select2-selection--single {
  border-color: #71dd8a;
}

.select2-container--default .select2-green.select2-dropdown .select2-search__field:focus,
.select2-container--default .select2-green .select2-dropdown .select2-search__field:focus,
.select2-container--default .select2-green .select2-search--inline .select2-search__field:focus,
.select2-green .select2-container--default.select2-dropdown .select2-search__field:focus,
.select2-green .select2-container--default .select2-dropdown .select2-search__field:focus,
.select2-green .select2-container--default .select2-search--inline .select2-search__field:focus {
  border: 1px solid #71dd8a;
}

.select2-container--default .select2-green .select2-results__option--highlighted,
.select2-green .select2-container--default .select2-results__option--highlighted {
  background-color: #28a745;
  color: #fff;
}

.select2-container--default .select2-green .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-green .select2-results__option--highlighted[aria-selected]:hover,
.select2-green .select2-container--default .select2-results__option--highlighted[aria-selected],
.select2-green .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {
  background-color: #259b40;
  color: #fff;
}

.select2-container--default .select2-green .select2-selection--multiple:focus,
.select2-green .select2-container--default .select2-selection--multiple:focus {
  border-color: #71dd8a;
}

.select2-container--default .select2-green .select2-selection--multiple .select2-selection__choice,
.select2-green .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #28a745;
  border-color: #23923d;
  color: #fff;
}

.select2-container--default .select2-green .select2-selection--multiple .select2-selection__choice__remove,
.select2-green .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  color: rgba(255, 255, 255, 0.7);
}

.select2-container--default .select2-green .select2-selection--multiple .select2-selection__choice__remove:hover,
.select2-green .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #fff;
}

.select2-container--default .select2-green.select2-container--focus .select2-selection--multiple,
.select2-green .select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #71dd8a;
}

.select2-teal + .select2-container--default.select2-container--open .select2-selection--single {
  border-color: #7eeaca;
}

.select2-teal + .select2-container--default.select2-container--focus .select2-selection--single {
  border-color: #7eeaca;
}

.select2-container--default .select2-teal.select2-dropdown .select2-search__field:focus,
.select2-container--default .select2-teal .select2-dropdown .select2-search__field:focus,
.select2-container--default .select2-teal .select2-search--inline .select2-search__field:focus,
.select2-teal .select2-container--default.select2-dropdown .select2-search__field:focus,
.select2-teal .select2-container--default .select2-dropdown .select2-search__field:focus,
.select2-teal .select2-container--default .select2-search--inline .select2-search__field:focus {
  border: 1px solid #7eeaca;
}

.select2-container--default .select2-teal .select2-results__option--highlighted,
.select2-teal .select2-container--default .select2-results__option--highlighted {
  background-color: #20c997;
  color: #fff;
}

.select2-container--default .select2-teal .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-teal .select2-results__option--highlighted[aria-selected]:hover,
.select2-teal .select2-container--default .select2-results__option--highlighted[aria-selected],
.select2-teal .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {
  background-color: #1ebc8d;
  color: #fff;
}

.select2-container--default .select2-teal .select2-selection--multiple:focus,
.select2-teal .select2-container--default .select2-selection--multiple:focus {
  border-color: #7eeaca;
}

.select2-container--default .select2-teal .select2-selection--multiple .select2-selection__choice,
.select2-teal .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #20c997;
  border-color: #1cb386;
  color: #fff;
}

.select2-container--default .select2-teal .select2-selection--multiple .select2-selection__choice__remove,
.select2-teal .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  color: rgba(255, 255, 255, 0.7);
}

.select2-container--default .select2-teal .select2-selection--multiple .select2-selection__choice__remove:hover,
.select2-teal .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #fff;
}

.select2-container--default .select2-teal.select2-container--focus .select2-selection--multiple,
.select2-teal .select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #7eeaca;
}

.select2-cyan + .select2-container--default.select2-container--open .select2-selection--single {
  border-color: #63d9ec;
}

.select2-cyan + .select2-container--default.select2-container--focus .select2-selection--single {
  border-color: #63d9ec;
}

.select2-container--default .select2-cyan.select2-dropdown .select2-search__field:focus,
.select2-container--default .select2-cyan .select2-dropdown .select2-search__field:focus,
.select2-container--default .select2-cyan .select2-search--inline .select2-search__field:focus,
.select2-cyan .select2-container--default.select2-dropdown .select2-search__field:focus,
.select2-cyan .select2-container--default .select2-dropdown .select2-search__field:focus,
.select2-cyan .select2-container--default .select2-search--inline .select2-search__field:focus {
  border: 1px solid #63d9ec;
}

.select2-container--default .select2-cyan .select2-results__option--highlighted,
.select2-cyan .select2-container--default .select2-results__option--highlighted {
  background-color: #17a2b8;
  color: #fff;
}

.select2-container--default .select2-cyan .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-cyan .select2-results__option--highlighted[aria-selected]:hover,
.select2-cyan .select2-container--default .select2-results__option--highlighted[aria-selected],
.select2-cyan .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {
  background-color: #1596aa;
  color: #fff;
}

.select2-container--default .select2-cyan .select2-selection--multiple:focus,
.select2-cyan .select2-container--default .select2-selection--multiple:focus {
  border-color: #63d9ec;
}

.select2-container--default .select2-cyan .select2-selection--multiple .select2-selection__choice,
.select2-cyan .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #17a2b8;
  border-color: #148ea1;
  color: #fff;
}

.select2-container--default .select2-cyan .select2-selection--multiple .select2-selection__choice__remove,
.select2-cyan .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  color: rgba(255, 255, 255, 0.7);
}

.select2-container--default .select2-cyan .select2-selection--multiple .select2-selection__choice__remove:hover,
.select2-cyan .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #fff;
}

.select2-container--default .select2-cyan.select2-container--focus .select2-selection--multiple,
.select2-cyan .select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #63d9ec;
}

.select2-white + .select2-container--default.select2-container--open .select2-selection--single {
  border-color: white;
}

.select2-white + .select2-container--default.select2-container--focus .select2-selection--single {
  border-color: white;
}

.select2-container--default .select2-white.select2-dropdown .select2-search__field:focus,
.select2-container--default .select2-white .select2-dropdown .select2-search__field:focus,
.select2-container--default .select2-white .select2-search--inline .select2-search__field:focus,
.select2-white .select2-container--default.select2-dropdown .select2-search__field:focus,
.select2-white .select2-container--default .select2-dropdown .select2-search__field:focus,
.select2-white .select2-container--default .select2-search--inline .select2-search__field:focus {
  border: 1px solid white;
}

.select2-container--default .select2-white .select2-results__option--highlighted,
.select2-white .select2-container--default .select2-results__option--highlighted {
  background-color: #fff;
  color: #1f2d3d;
}

.select2-container--default .select2-white .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-white .select2-results__option--highlighted[aria-selected]:hover,
.select2-white .select2-container--default .select2-results__option--highlighted[aria-selected],
.select2-white .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {
  background-color: #f7f7f7;
  color: #1f2d3d;
}

.select2-container--default .select2-white .select2-selection--multiple:focus,
.select2-white .select2-container--default .select2-selection--multiple:focus {
  border-color: white;
}

.select2-container--default .select2-white .select2-selection--multiple .select2-selection__choice,
.select2-white .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #fff;
  border-color: #f2f2f2;
  color: #1f2d3d;
}

.select2-container--default .select2-white .select2-selection--multiple .select2-selection__choice__remove,
.select2-white .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  color: rgba(31, 45, 61, 0.7);
}

.select2-container--default .select2-white .select2-selection--multiple .select2-selection__choice__remove:hover,
.select2-white .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #1f2d3d;
}

.select2-container--default .select2-white.select2-container--focus .select2-selection--multiple,
.select2-white .select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: white;
}

.select2-gray + .select2-container--default.select2-container--open .select2-selection--single {
  border-color: #afb5ba;
}

.select2-gray + .select2-container--default.select2-container--focus .select2-selection--single {
  border-color: #afb5ba;
}

.select2-container--default .select2-gray.select2-dropdown .select2-search__field:focus,
.select2-container--default .select2-gray .select2-dropdown .select2-search__field:focus,
.select2-container--default .select2-gray .select2-search--inline .select2-search__field:focus,
.select2-gray .select2-container--default.select2-dropdown .select2-search__field:focus,
.select2-gray .select2-container--default .select2-dropdown .select2-search__field:focus,
.select2-gray .select2-container--default .select2-search--inline .select2-search__field:focus {
  border: 1px solid #afb5ba;
}

.select2-container--default .select2-gray .select2-results__option--highlighted,
.select2-gray .select2-container--default .select2-results__option--highlighted {
  background-color: #6c757d;
  color: #fff;
}

.select2-container--default .select2-gray .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-gray .select2-results__option--highlighted[aria-selected]:hover,
.select2-gray .select2-container--default .select2-results__option--highlighted[aria-selected],
.select2-gray .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {
  background-color: #656d75;
  color: #fff;
}

.select2-container--default .select2-gray .select2-selection--multiple:focus,
.select2-gray .select2-container--default .select2-selection--multiple:focus {
  border-color: #afb5ba;
}

.select2-container--default .select2-gray .select2-selection--multiple .select2-selection__choice,
.select2-gray .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #6c757d;
  border-color: #60686f;
  color: #fff;
}

.select2-container--default .select2-gray .select2-selection--multiple .select2-selection__choice__remove,
.select2-gray .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  color: rgba(255, 255, 255, 0.7);
}

.select2-container--default .select2-gray .select2-selection--multiple .select2-selection__choice__remove:hover,
.select2-gray .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #fff;
}

.select2-container--default .select2-gray.select2-container--focus .select2-selection--multiple,
.select2-gray .select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #afb5ba;
}

.select2-gray-dark + .select2-container--default.select2-container--open .select2-selection--single {
  border-color: #6d7a86;
}

.select2-gray-dark + .select2-container--default.select2-container--focus .select2-selection--single {
  border-color: #6d7a86;
}

.select2-container--default .select2-gray-dark.select2-dropdown .select2-search__field:focus,
.select2-container--default .select2-gray-dark .select2-dropdown .select2-search__field:focus,
.select2-container--default .select2-gray-dark .select2-search--inline .select2-search__field:focus,
.select2-gray-dark .select2-container--default.select2-dropdown .select2-search__field:focus,
.select2-gray-dark .select2-container--default .select2-dropdown .select2-search__field:focus,
.select2-gray-dark .select2-container--default .select2-search--inline .select2-search__field:focus {
  border: 1px solid #6d7a86;
}

.select2-container--default .select2-gray-dark .select2-results__option--highlighted,
.select2-gray-dark .select2-container--default .select2-results__option--highlighted {
  background-color: #343a40;
  color: #fff;
}

.select2-container--default .select2-gray-dark .select2-results__option--highlighted[aria-selected], .select2-container--default .select2-gray-dark .select2-results__option--highlighted[aria-selected]:hover,
.select2-gray-dark .select2-container--default .select2-results__option--highlighted[aria-selected],
.select2-gray-dark .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {
  background-color: #2d3238;
  color: #fff;
}

.select2-container--default .select2-gray-dark .select2-selection--multiple:focus,
.select2-gray-dark .select2-container--default .select2-selection--multiple:focus {
  border-color: #6d7a86;
}

.select2-container--default .select2-gray-dark .select2-selection--multiple .select2-selection__choice,
.select2-gray-dark .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #343a40;
  border-color: #292d32;
  color: #fff;
}

.select2-container--default .select2-gray-dark .select2-selection--multiple .select2-selection__choice__remove,
.select2-gray-dark .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  color: rgba(255, 255, 255, 0.7);
}

.select2-container--default .select2-gray-dark .select2-selection--multiple .select2-selection__choice__remove:hover,
.select2-gray-dark .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #fff;
}

.select2-container--default .select2-gray-dark.select2-container--focus .select2-selection--multiple,
.select2-gray-dark .select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #6d7a86;
}

.dark-mode .select2-selection {
  background-color: #343a40;
  border-color: #6c757d;
}

.dark-mode .select2-container--disabled .select2-selection--single {
  background-color: #454d55;
}

.dark-mode .select2-selection--single {
  background-color: #343a40;
  border-color: #6c757d;
}

.dark-mode .select2-selection--single .select2-selection__rendered {
  color: #fff;
}

.dark-mode .select2-dropdown .select2-search__field,
.dark-mode .select2-search--inline .select2-search__field {
  background-color: #343a40;
  border-color: #6c757d;
  color: white;
}

.dark-mode .select2-dropdown {
  background-color: #343a40;
  border-color: #6c757d;
  color: white;
}

.dark-mode .select2-results__option[aria-selected="true"] {
  background-color: #3f474e !important;
  color: #dee2e6;
}

.dark-mode .select2-container .select2-search--inline .select2-search__field {
  background-color: transparent;
  color: #fff;
}

.dark-mode .select2-container--bootstrap4 .select2-selection--multiple .select2-selection__choice {
  color: #fff;
}

.dark-mode .select2-primary + .select2-container--default.select2-container--open .select2-selection--single {
  border-color: #85a7ca;
}

.dark-mode .select2-primary + .select2-container--default.select2-container--focus .select2-selection--single {
  border-color: #85a7ca;
}

.select2-container--default .dark-mode .select2-primary.select2-dropdown .select2-search__field:focus,
.select2-container--default .dark-mode .select2-primary .select2-dropdown .select2-search__field:focus,
.select2-container--default .dark-mode .select2-primary .select2-search--inline .select2-search__field:focus,
.dark-mode .select2-primary .select2-container--default.select2-dropdown .select2-search__field:focus,
.dark-mode .select2-primary .select2-container--default .select2-dropdown .select2-search__field:focus,
.dark-mode .select2-primary .select2-container--default .select2-search--inline .select2-search__field:focus {
  border: 1px solid #85a7ca;
}

.select2-container--default .dark-mode .select2-primary .select2-results__option--highlighted,
.dark-mode .select2-primary .select2-container--default .select2-results__option--highlighted {
  background-color: #3f6791;
  color: #fff;
}

.select2-container--default .dark-mode .select2-primary .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-primary .select2-results__option--highlighted[aria-selected]:hover,
.dark-mode .select2-primary .select2-container--default .select2-results__option--highlighted[aria-selected],
.dark-mode .select2-primary .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {
  background-color: #3a5f86;
  color: #fff;
}

.select2-container--default .dark-mode .select2-primary .select2-selection--multiple:focus,
.dark-mode .select2-primary .select2-container--default .select2-selection--multiple:focus {
  border-color: #85a7ca;
}

.select2-container--default .dark-mode .select2-primary .select2-selection--multiple .select2-selection__choice,
.dark-mode .select2-primary .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #3f6791;
  border-color: #375a7f;
  color: #fff;
}

.select2-container--default .dark-mode .select2-primary .select2-selection--multiple .select2-selection__choice__remove,
.dark-mode .select2-primary .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  color: rgba(255, 255, 255, 0.7);
}

.select2-container--default .dark-mode .select2-primary .select2-selection--multiple .select2-selection__choice__remove:hover,
.dark-mode .select2-primary .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #fff;
}

.select2-container--default .dark-mode .select2-primary.select2-container--focus .select2-selection--multiple,
.dark-mode .select2-primary .select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #85a7ca;
}

.dark-mode .select2-secondary + .select2-container--default.select2-container--open .select2-selection--single {
  border-color: #afb5ba;
}

.dark-mode .select2-secondary + .select2-container--default.select2-container--focus .select2-selection--single {
  border-color: #afb5ba;
}

.select2-container--default .dark-mode .select2-secondary.select2-dropdown .select2-search__field:focus,
.select2-container--default .dark-mode .select2-secondary .select2-dropdown .select2-search__field:focus,
.select2-container--default .dark-mode .select2-secondary .select2-search--inline .select2-search__field:focus,
.dark-mode .select2-secondary .select2-container--default.select2-dropdown .select2-search__field:focus,
.dark-mode .select2-secondary .select2-container--default .select2-dropdown .select2-search__field:focus,
.dark-mode .select2-secondary .select2-container--default .select2-search--inline .select2-search__field:focus {
  border: 1px solid #afb5ba;
}

.select2-container--default .dark-mode .select2-secondary .select2-results__option--highlighted,
.dark-mode .select2-secondary .select2-container--default .select2-results__option--highlighted {
  background-color: #6c757d;
  color: #fff;
}

.select2-container--default .dark-mode .select2-secondary .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-secondary .select2-results__option--highlighted[aria-selected]:hover,
.dark-mode .select2-secondary .select2-container--default .select2-results__option--highlighted[aria-selected],
.dark-mode .select2-secondary .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {
  background-color: #656d75;
  color: #fff;
}

.select2-container--default .dark-mode .select2-secondary .select2-selection--multiple:focus,
.dark-mode .select2-secondary .select2-container--default .select2-selection--multiple:focus {
  border-color: #afb5ba;
}

.select2-container--default .dark-mode .select2-secondary .select2-selection--multiple .select2-selection__choice,
.dark-mode .select2-secondary .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #6c757d;
  border-color: #60686f;
  color: #fff;
}

.select2-container--default .dark-mode .select2-secondary .select2-selection--multiple .select2-selection__choice__remove,
.dark-mode .select2-secondary .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  color: rgba(255, 255, 255, 0.7);
}

.select2-container--default .dark-mode .select2-secondary .select2-selection--multiple .select2-selection__choice__remove:hover,
.dark-mode .select2-secondary .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #fff;
}

.select2-container--default .dark-mode .select2-secondary.select2-container--focus .select2-selection--multiple,
.dark-mode .select2-secondary .select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #afb5ba;
}

.dark-mode .select2-success + .select2-container--default.select2-container--open .select2-selection--single {
  border-color: #3dffcd;
}

.dark-mode .select2-success + .select2-container--default.select2-container--focus .select2-selection--single {
  border-color: #3dffcd;
}

.select2-container--default .dark-mode .select2-success.select2-dropdown .select2-search__field:focus,
.select2-container--default .dark-mode .select2-success .select2-dropdown .select2-search__field:focus,
.select2-container--default .dark-mode .select2-success .select2-search--inline .select2-search__field:focus,
.dark-mode .select2-success .select2-container--default.select2-dropdown .select2-search__field:focus,
.dark-mode .select2-success .select2-container--default .select2-dropdown .select2-search__field:focus,
.dark-mode .select2-success .select2-container--default .select2-search--inline .select2-search__field:focus {
  border: 1px solid #3dffcd;
}

.select2-container--default .dark-mode .select2-success .select2-results__option--highlighted,
.dark-mode .select2-success .select2-container--default .select2-results__option--highlighted {
  background-color: #00bc8c;
  color: #fff;
}

.select2-container--default .dark-mode .select2-success .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-success .select2-results__option--highlighted[aria-selected]:hover,
.dark-mode .select2-success .select2-container--default .select2-results__option--highlighted[aria-selected],
.dark-mode .select2-success .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {
  background-color: #00ad81;
  color: #fff;
}

.select2-container--default .dark-mode .select2-success .select2-selection--multiple:focus,
.dark-mode .select2-success .select2-container--default .select2-selection--multiple:focus {
  border-color: #3dffcd;
}

.select2-container--default .dark-mode .select2-success .select2-selection--multiple .select2-selection__choice,
.dark-mode .select2-success .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #00bc8c;
  border-color: #00a379;
  color: #fff;
}

.select2-container--default .dark-mode .select2-success .select2-selection--multiple .select2-selection__choice__remove,
.dark-mode .select2-success .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  color: rgba(255, 255, 255, 0.7);
}

.select2-container--default .dark-mode .select2-success .select2-selection--multiple .select2-selection__choice__remove:hover,
.dark-mode .select2-success .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #fff;
}

.select2-container--default .dark-mode .select2-success.select2-container--focus .select2-selection--multiple,
.dark-mode .select2-success .select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #3dffcd;
}

.dark-mode .select2-info + .select2-container--default.select2-container--open .select2-selection--single {
  border-color: #a0cfee;
}

.dark-mode .select2-info + .select2-container--default.select2-container--focus .select2-selection--single {
  border-color: #a0cfee;
}

.select2-container--default .dark-mode .select2-info.select2-dropdown .select2-search__field:focus,
.select2-container--default .dark-mode .select2-info .select2-dropdown .select2-search__field:focus,
.select2-container--default .dark-mode .select2-info .select2-search--inline .select2-search__field:focus,
.dark-mode .select2-info .select2-container--default.select2-dropdown .select2-search__field:focus,
.dark-mode .select2-info .select2-container--default .select2-dropdown .select2-search__field:focus,
.dark-mode .select2-info .select2-container--default .select2-search--inline .select2-search__field:focus {
  border: 1px solid #a0cfee;
}

.select2-container--default .dark-mode .select2-info .select2-results__option--highlighted,
.dark-mode .select2-info .select2-container--default .select2-results__option--highlighted {
  background-color: #3498db;
  color: #fff;
}

.select2-container--default .dark-mode .select2-info .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-info .select2-results__option--highlighted[aria-selected]:hover,
.dark-mode .select2-info .select2-container--default .select2-results__option--highlighted[aria-selected],
.dark-mode .select2-info .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {
  background-color: #2791d9;
  color: #fff;
}

.select2-container--default .dark-mode .select2-info .select2-selection--multiple:focus,
.dark-mode .select2-info .select2-container--default .select2-selection--multiple:focus {
  border-color: #a0cfee;
}

.select2-container--default .dark-mode .select2-info .select2-selection--multiple .select2-selection__choice,
.dark-mode .select2-info .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #3498db;
  border-color: #258cd1;
  color: #fff;
}

.select2-container--default .dark-mode .select2-info .select2-selection--multiple .select2-selection__choice__remove,
.dark-mode .select2-info .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  color: rgba(255, 255, 255, 0.7);
}

.select2-container--default .dark-mode .select2-info .select2-selection--multiple .select2-selection__choice__remove:hover,
.dark-mode .select2-info .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #fff;
}

.select2-container--default .dark-mode .select2-info.select2-container--focus .select2-selection--multiple,
.dark-mode .select2-info .select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #a0cfee;
}

.dark-mode .select2-warning + .select2-container--default.select2-container--open .select2-selection--single {
  border-color: #f9cf8b;
}

.dark-mode .select2-warning + .select2-container--default.select2-container--focus .select2-selection--single {
  border-color: #f9cf8b;
}

.select2-container--default .dark-mode .select2-warning.select2-dropdown .select2-search__field:focus,
.select2-container--default .dark-mode .select2-warning .select2-dropdown .select2-search__field:focus,
.select2-container--default .dark-mode .select2-warning .select2-search--inline .select2-search__field:focus,
.dark-mode .select2-warning .select2-container--default.select2-dropdown .select2-search__field:focus,
.dark-mode .select2-warning .select2-container--default .select2-dropdown .select2-search__field:focus,
.dark-mode .select2-warning .select2-container--default .select2-search--inline .select2-search__field:focus {
  border: 1px solid #f9cf8b;
}

.select2-container--default .dark-mode .select2-warning .select2-results__option--highlighted,
.dark-mode .select2-warning .select2-container--default .select2-results__option--highlighted {
  background-color: #f39c12;
  color: #1f2d3d;
}

.select2-container--default .dark-mode .select2-warning .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-warning .select2-results__option--highlighted[aria-selected]:hover,
.dark-mode .select2-warning .select2-container--default .select2-results__option--highlighted[aria-selected],
.dark-mode .select2-warning .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {
  background-color: #ea940c;
  color: #1f2d3d;
}

.select2-container--default .dark-mode .select2-warning .select2-selection--multiple:focus,
.dark-mode .select2-warning .select2-container--default .select2-selection--multiple:focus {
  border-color: #f9cf8b;
}

.select2-container--default .dark-mode .select2-warning .select2-selection--multiple .select2-selection__choice,
.dark-mode .select2-warning .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #f39c12;
  border-color: #e08e0b;
  color: #1f2d3d;
}

.select2-container--default .dark-mode .select2-warning .select2-selection--multiple .select2-selection__choice__remove,
.dark-mode .select2-warning .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  color: rgba(31, 45, 61, 0.7);
}

.select2-container--default .dark-mode .select2-warning .select2-selection--multiple .select2-selection__choice__remove:hover,
.dark-mode .select2-warning .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #1f2d3d;
}

.select2-container--default .dark-mode .select2-warning.select2-container--focus .select2-selection--multiple,
.dark-mode .select2-warning .select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #f9cf8b;
}

.dark-mode .select2-danger + .select2-container--default.select2-container--open .select2-selection--single {
  border-color: #f5b4ae;
}

.dark-mode .select2-danger + .select2-container--default.select2-container--focus .select2-selection--single {
  border-color: #f5b4ae;
}

.select2-container--default .dark-mode .select2-danger.select2-dropdown .select2-search__field:focus,
.select2-container--default .dark-mode .select2-danger .select2-dropdown .select2-search__field:focus,
.select2-container--default .dark-mode .select2-danger .select2-search--inline .select2-search__field:focus,
.dark-mode .select2-danger .select2-container--default.select2-dropdown .select2-search__field:focus,
.dark-mode .select2-danger .select2-container--default .select2-dropdown .select2-search__field:focus,
.dark-mode .select2-danger .select2-container--default .select2-search--inline .select2-search__field:focus {
  border: 1px solid #f5b4ae;
}

.select2-container--default .dark-mode .select2-danger .select2-results__option--highlighted,
.dark-mode .select2-danger .select2-container--default .select2-results__option--highlighted {
  background-color: #e74c3c;
  color: #fff;
}

.select2-container--default .dark-mode .select2-danger .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-danger .select2-results__option--highlighted[aria-selected]:hover,
.dark-mode .select2-danger .select2-container--default .select2-results__option--highlighted[aria-selected],
.dark-mode .select2-danger .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {
  background-color: #e53f2e;
  color: #fff;
}

.select2-container--default .dark-mode .select2-danger .select2-selection--multiple:focus,
.dark-mode .select2-danger .select2-container--default .select2-selection--multiple:focus {
  border-color: #f5b4ae;
}

.select2-container--default .dark-mode .select2-danger .select2-selection--multiple .select2-selection__choice,
.dark-mode .select2-danger .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #e74c3c;
  border-color: #e43725;
  color: #fff;
}

.select2-container--default .dark-mode .select2-danger .select2-selection--multiple .select2-selection__choice__remove,
.dark-mode .select2-danger .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  color: rgba(255, 255, 255, 0.7);
}

.select2-container--default .dark-mode .select2-danger .select2-selection--multiple .select2-selection__choice__remove:hover,
.dark-mode .select2-danger .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #fff;
}

.select2-container--default .dark-mode .select2-danger.select2-container--focus .select2-selection--multiple,
.dark-mode .select2-danger .select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #f5b4ae;
}

.dark-mode .select2-light + .select2-container--default.select2-container--open .select2-selection--single {
  border-color: white;
}

.dark-mode .select2-light + .select2-container--default.select2-container--focus .select2-selection--single {
  border-color: white;
}

.select2-container--default .dark-mode .select2-light.select2-dropdown .select2-search__field:focus,
.select2-container--default .dark-mode .select2-light .select2-dropdown .select2-search__field:focus,
.select2-container--default .dark-mode .select2-light .select2-search--inline .select2-search__field:focus,
.dark-mode .select2-light .select2-container--default.select2-dropdown .select2-search__field:focus,
.dark-mode .select2-light .select2-container--default .select2-dropdown .select2-search__field:focus,
.dark-mode .select2-light .select2-container--default .select2-search--inline .select2-search__field:focus {
  border: 1px solid white;
}

.select2-container--default .dark-mode .select2-light .select2-results__option--highlighted,
.dark-mode .select2-light .select2-container--default .select2-results__option--highlighted {
  background-color: #f8f9fa;
  color: #1f2d3d;
}

.select2-container--default .dark-mode .select2-light .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-light .select2-results__option--highlighted[aria-selected]:hover,
.dark-mode .select2-light .select2-container--default .select2-results__option--highlighted[aria-selected],
.dark-mode .select2-light .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {
  background-color: #eff1f4;
  color: #1f2d3d;
}

.select2-container--default .dark-mode .select2-light .select2-selection--multiple:focus,
.dark-mode .select2-light .select2-container--default .select2-selection--multiple:focus {
  border-color: white;
}

.select2-container--default .dark-mode .select2-light .select2-selection--multiple .select2-selection__choice,
.dark-mode .select2-light .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #f8f9fa;
  border-color: #e9ecef;
  color: #1f2d3d;
}

.select2-container--default .dark-mode .select2-light .select2-selection--multiple .select2-selection__choice__remove,
.dark-mode .select2-light .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  color: rgba(31, 45, 61, 0.7);
}

.select2-container--default .dark-mode .select2-light .select2-selection--multiple .select2-selection__choice__remove:hover,
.dark-mode .select2-light .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #1f2d3d;
}

.select2-container--default .dark-mode .select2-light.select2-container--focus .select2-selection--multiple,
.dark-mode .select2-light .select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: white;
}

.dark-mode .select2-dark + .select2-container--default.select2-container--open .select2-selection--single {
  border-color: #6d7a86;
}

.dark-mode .select2-dark + .select2-container--default.select2-container--focus .select2-selection--single {
  border-color: #6d7a86;
}

.select2-container--default .dark-mode .select2-dark.select2-dropdown .select2-search__field:focus,
.select2-container--default .dark-mode .select2-dark .select2-dropdown .select2-search__field:focus,
.select2-container--default .dark-mode .select2-dark .select2-search--inline .select2-search__field:focus,
.dark-mode .select2-dark .select2-container--default.select2-dropdown .select2-search__field:focus,
.dark-mode .select2-dark .select2-container--default .select2-dropdown .select2-search__field:focus,
.dark-mode .select2-dark .select2-container--default .select2-search--inline .select2-search__field:focus {
  border: 1px solid #6d7a86;
}

.select2-container--default .dark-mode .select2-dark .select2-results__option--highlighted,
.dark-mode .select2-dark .select2-container--default .select2-results__option--highlighted {
  background-color: #343a40;
  color: #fff;
}

.select2-container--default .dark-mode .select2-dark .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-dark .select2-results__option--highlighted[aria-selected]:hover,
.dark-mode .select2-dark .select2-container--default .select2-results__option--highlighted[aria-selected],
.dark-mode .select2-dark .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {
  background-color: #2d3238;
  color: #fff;
}

.select2-container--default .dark-mode .select2-dark .select2-selection--multiple:focus,
.dark-mode .select2-dark .select2-container--default .select2-selection--multiple:focus {
  border-color: #6d7a86;
}

.select2-container--default .dark-mode .select2-dark .select2-selection--multiple .select2-selection__choice,
.dark-mode .select2-dark .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #343a40;
  border-color: #292d32;
  color: #fff;
}

.select2-container--default .dark-mode .select2-dark .select2-selection--multiple .select2-selection__choice__remove,
.dark-mode .select2-dark .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  color: rgba(255, 255, 255, 0.7);
}

.select2-container--default .dark-mode .select2-dark .select2-selection--multiple .select2-selection__choice__remove:hover,
.dark-mode .select2-dark .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #fff;
}

.select2-container--default .dark-mode .select2-dark.select2-container--focus .select2-selection--multiple,
.dark-mode .select2-dark .select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #6d7a86;
}

.dark-mode .select2-lightblue + .select2-container--default.select2-container--open .select2-selection--single {
  border-color: #e6f1f7;
}

.dark-mode .select2-lightblue + .select2-container--default.select2-container--focus .select2-selection--single {
  border-color: #e6f1f7;
}

.select2-container--default .dark-mode .select2-lightblue.select2-dropdown .select2-search__field:focus,
.select2-container--default .dark-mode .select2-lightblue .select2-dropdown .select2-search__field:focus,
.select2-container--default .dark-mode .select2-lightblue .select2-search--inline .select2-search__field:focus,
.dark-mode .select2-lightblue .select2-container--default.select2-dropdown .select2-search__field:focus,
.dark-mode .select2-lightblue .select2-container--default .select2-dropdown .select2-search__field:focus,
.dark-mode .select2-lightblue .select2-container--default .select2-search--inline .select2-search__field:focus {
  border: 1px solid #e6f1f7;
}

.select2-container--default .dark-mode .select2-lightblue .select2-results__option--highlighted,
.dark-mode .select2-lightblue .select2-container--default .select2-results__option--highlighted {
  background-color: #86bad8;
  color: #1f2d3d;
}

.select2-container--default .dark-mode .select2-lightblue .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-lightblue .select2-results__option--highlighted[aria-selected]:hover,
.dark-mode .select2-lightblue .select2-container--default .select2-results__option--highlighted[aria-selected],
.dark-mode .select2-lightblue .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {
  background-color: #7ab3d5;
  color: #1f2d3d;
}

.select2-container--default .dark-mode .select2-lightblue .select2-selection--multiple:focus,
.dark-mode .select2-lightblue .select2-container--default .select2-selection--multiple:focus {
  border-color: #e6f1f7;
}

.select2-container--default .dark-mode .select2-lightblue .select2-selection--multiple .select2-selection__choice,
.dark-mode .select2-lightblue .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #86bad8;
  border-color: #72afd2;
  color: #1f2d3d;
}

.select2-container--default .dark-mode .select2-lightblue .select2-selection--multiple .select2-selection__choice__remove,
.dark-mode .select2-lightblue .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  color: rgba(31, 45, 61, 0.7);
}

.select2-container--default .dark-mode .select2-lightblue .select2-selection--multiple .select2-selection__choice__remove:hover,
.dark-mode .select2-lightblue .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #1f2d3d;
}

.select2-container--default .dark-mode .select2-lightblue.select2-container--focus .select2-selection--multiple,
.dark-mode .select2-lightblue .select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #e6f1f7;
}

.dark-mode .select2-navy + .select2-container--default.select2-container--open .select2-selection--single {
  border-color: #006ad8;
}

.dark-mode .select2-navy + .select2-container--default.select2-container--focus .select2-selection--single {
  border-color: #006ad8;
}

.select2-container--default .dark-mode .select2-navy.select2-dropdown .select2-search__field:focus,
.select2-container--default .dark-mode .select2-navy .select2-dropdown .select2-search__field:focus,
.select2-container--default .dark-mode .select2-navy .select2-search--inline .select2-search__field:focus,
.dark-mode .select2-navy .select2-container--default.select2-dropdown .select2-search__field:focus,
.dark-mode .select2-navy .select2-container--default .select2-dropdown .select2-search__field:focus,
.dark-mode .select2-navy .select2-container--default .select2-search--inline .select2-search__field:focus {
  border: 1px solid #006ad8;
}

.select2-container--default .dark-mode .select2-navy .select2-results__option--highlighted,
.dark-mode .select2-navy .select2-container--default .select2-results__option--highlighted {
  background-color: #002c59;
  color: #fff;
}

.select2-container--default .dark-mode .select2-navy .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-navy .select2-results__option--highlighted[aria-selected]:hover,
.dark-mode .select2-navy .select2-container--default .select2-results__option--highlighted[aria-selected],
.dark-mode .select2-navy .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {
  background-color: #002449;
  color: #fff;
}

.select2-container--default .dark-mode .select2-navy .select2-selection--multiple:focus,
.dark-mode .select2-navy .select2-container--default .select2-selection--multiple:focus {
  border-color: #006ad8;
}

.select2-container--default .dark-mode .select2-navy .select2-selection--multiple .select2-selection__choice,
.dark-mode .select2-navy .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #002c59;
  border-color: #001f3f;
  color: #fff;
}

.select2-container--default .dark-mode .select2-navy .select2-selection--multiple .select2-selection__choice__remove,
.dark-mode .select2-navy .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  color: rgba(255, 255, 255, 0.7);
}

.select2-container--default .dark-mode .select2-navy .select2-selection--multiple .select2-selection__choice__remove:hover,
.dark-mode .select2-navy .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #fff;
}

.select2-container--default .dark-mode .select2-navy.select2-container--focus .select2-selection--multiple,
.dark-mode .select2-navy .select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #006ad8;
}

.dark-mode .select2-olive + .select2-container--default.select2-container--open .select2-selection--single {
  border-color: #cfecdf;
}

.dark-mode .select2-olive + .select2-container--default.select2-container--focus .select2-selection--single {
  border-color: #cfecdf;
}

.select2-container--default .dark-mode .select2-olive.select2-dropdown .select2-search__field:focus,
.select2-container--default .dark-mode .select2-olive .select2-dropdown .select2-search__field:focus,
.select2-container--default .dark-mode .select2-olive .select2-search--inline .select2-search__field:focus,
.dark-mode .select2-olive .select2-container--default.select2-dropdown .select2-search__field:focus,
.dark-mode .select2-olive .select2-container--default .select2-dropdown .select2-search__field:focus,
.dark-mode .select2-olive .select2-container--default .select2-search--inline .select2-search__field:focus {
  border: 1px solid #cfecdf;
}

.select2-container--default .dark-mode .select2-olive .select2-results__option--highlighted,
.dark-mode .select2-olive .select2-container--default .select2-results__option--highlighted {
  background-color: #74c8a3;
  color: #1f2d3d;
}

.select2-container--default .dark-mode .select2-olive .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-olive .select2-results__option--highlighted[aria-selected]:hover,
.dark-mode .select2-olive .select2-container--default .select2-results__option--highlighted[aria-selected],
.dark-mode .select2-olive .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {
  background-color: #69c39b;
  color: #1f2d3d;
}

.select2-container--default .dark-mode .select2-olive .select2-selection--multiple:focus,
.dark-mode .select2-olive .select2-container--default .select2-selection--multiple:focus {
  border-color: #cfecdf;
}

.select2-container--default .dark-mode .select2-olive .select2-selection--multiple .select2-selection__choice,
.dark-mode .select2-olive .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #74c8a3;
  border-color: #62c096;
  color: #1f2d3d;
}

.select2-container--default .dark-mode .select2-olive .select2-selection--multiple .select2-selection__choice__remove,
.dark-mode .select2-olive .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  color: rgba(31, 45, 61, 0.7);
}

.select2-container--default .dark-mode .select2-olive .select2-selection--multiple .select2-selection__choice__remove:hover,
.dark-mode .select2-olive .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #1f2d3d;
}

.select2-container--default .dark-mode .select2-olive.select2-container--focus .select2-selection--multiple,
.dark-mode .select2-olive .select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #cfecdf;
}

.dark-mode .select2-lime + .select2-container--default.select2-container--open .select2-selection--single {
  border-color: #e7fff1;
}

.dark-mode .select2-lime + .select2-container--default.select2-container--focus .select2-selection--single {
  border-color: #e7fff1;
}

.select2-container--default .dark-mode .select2-lime.select2-dropdown .select2-search__field:focus,
.select2-container--default .dark-mode .select2-lime .select2-dropdown .select2-search__field:focus,
.select2-container--default .dark-mode .select2-lime .select2-search--inline .select2-search__field:focus,
.dark-mode .select2-lime .select2-container--default.select2-dropdown .select2-search__field:focus,
.dark-mode .select2-lime .select2-container--default .select2-dropdown .select2-search__field:focus,
.dark-mode .select2-lime .select2-container--default .select2-search--inline .select2-search__field:focus {
  border: 1px solid #e7fff1;
}

.select2-container--default .dark-mode .select2-lime .select2-results__option--highlighted,
.dark-mode .select2-lime .select2-container--default .select2-results__option--highlighted {
  background-color: #67ffa9;
  color: #1f2d3d;
}

.select2-container--default .dark-mode .select2-lime .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-lime .select2-results__option--highlighted[aria-selected]:hover,
.dark-mode .select2-lime .select2-container--default .select2-results__option--highlighted[aria-selected],
.dark-mode .select2-lime .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {
  background-color: #58ffa1;
  color: #1f2d3d;
}

.select2-container--default .dark-mode .select2-lime .select2-selection--multiple:focus,
.dark-mode .select2-lime .select2-container--default .select2-selection--multiple:focus {
  border-color: #e7fff1;
}

.select2-container--default .dark-mode .select2-lime .select2-selection--multiple .select2-selection__choice,
.dark-mode .select2-lime .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #67ffa9;
  border-color: #4eff9b;
  color: #1f2d3d;
}

.select2-container--default .dark-mode .select2-lime .select2-selection--multiple .select2-selection__choice__remove,
.dark-mode .select2-lime .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  color: rgba(31, 45, 61, 0.7);
}

.select2-container--default .dark-mode .select2-lime .select2-selection--multiple .select2-selection__choice__remove:hover,
.dark-mode .select2-lime .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #1f2d3d;
}

.select2-container--default .dark-mode .select2-lime.select2-container--focus .select2-selection--multiple,
.dark-mode .select2-lime .select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #e7fff1;
}

.dark-mode .select2-fuchsia + .select2-container--default.select2-container--open .select2-selection--single {
  border-color: #feeaf9;
}

.dark-mode .select2-fuchsia + .select2-container--default.select2-container--focus .select2-selection--single {
  border-color: #feeaf9;
}

.select2-container--default .dark-mode .select2-fuchsia.select2-dropdown .select2-search__field:focus,
.select2-container--default .dark-mode .select2-fuchsia .select2-dropdown .select2-search__field:focus,
.select2-container--default .dark-mode .select2-fuchsia .select2-search--inline .select2-search__field:focus,
.dark-mode .select2-fuchsia .select2-container--default.select2-dropdown .select2-search__field:focus,
.dark-mode .select2-fuchsia .select2-container--default .select2-dropdown .select2-search__field:focus,
.dark-mode .select2-fuchsia .select2-container--default .select2-search--inline .select2-search__field:focus {
  border: 1px solid #feeaf9;
}

.select2-container--default .dark-mode .select2-fuchsia .select2-results__option--highlighted,
.dark-mode .select2-fuchsia .select2-container--default .select2-results__option--highlighted {
  background-color: #f672d8;
  color: #1f2d3d;
}

.select2-container--default .dark-mode .select2-fuchsia .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-fuchsia .select2-results__option--highlighted[aria-selected]:hover,
.dark-mode .select2-fuchsia .select2-container--default .select2-results__option--highlighted[aria-selected],
.dark-mode .select2-fuchsia .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {
  background-color: #f564d4;
  color: #1f2d3d;
}

.select2-container--default .dark-mode .select2-fuchsia .select2-selection--multiple:focus,
.dark-mode .select2-fuchsia .select2-container--default .select2-selection--multiple:focus {
  border-color: #feeaf9;
}

.select2-container--default .dark-mode .select2-fuchsia .select2-selection--multiple .select2-selection__choice,
.dark-mode .select2-fuchsia .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #f672d8;
  border-color: #f55ad2;
  color: #1f2d3d;
}

.select2-container--default .dark-mode .select2-fuchsia .select2-selection--multiple .select2-selection__choice__remove,
.dark-mode .select2-fuchsia .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  color: rgba(31, 45, 61, 0.7);
}

.select2-container--default .dark-mode .select2-fuchsia .select2-selection--multiple .select2-selection__choice__remove:hover,
.dark-mode .select2-fuchsia .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #1f2d3d;
}

.select2-container--default .dark-mode .select2-fuchsia.select2-container--focus .select2-selection--multiple,
.dark-mode .select2-fuchsia .select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #feeaf9;
}

.dark-mode .select2-maroon + .select2-container--default.select2-container--open .select2-selection--single {
  border-color: #fbdee8;
}

.dark-mode .select2-maroon + .select2-container--default.select2-container--focus .select2-selection--single {
  border-color: #fbdee8;
}

.select2-container--default .dark-mode .select2-maroon.select2-dropdown .select2-search__field:focus,
.select2-container--default .dark-mode .select2-maroon .select2-dropdown .select2-search__field:focus,
.select2-container--default .dark-mode .select2-maroon .select2-search--inline .select2-search__field:focus,
.dark-mode .select2-maroon .select2-container--default.select2-dropdown .select2-search__field:focus,
.dark-mode .select2-maroon .select2-container--default .select2-dropdown .select2-search__field:focus,
.dark-mode .select2-maroon .select2-container--default .select2-search--inline .select2-search__field:focus {
  border: 1px solid #fbdee8;
}

.select2-container--default .dark-mode .select2-maroon .select2-results__option--highlighted,
.dark-mode .select2-maroon .select2-container--default .select2-results__option--highlighted {
  background-color: #ed6c9b;
  color: #1f2d3d;
}

.select2-container--default .dark-mode .select2-maroon .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-maroon .select2-results__option--highlighted[aria-selected]:hover,
.dark-mode .select2-maroon .select2-container--default .select2-results__option--highlighted[aria-selected],
.dark-mode .select2-maroon .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {
  background-color: #eb5f92;
  color: #fff;
}

.select2-container--default .dark-mode .select2-maroon .select2-selection--multiple:focus,
.dark-mode .select2-maroon .select2-container--default .select2-selection--multiple:focus {
  border-color: #fbdee8;
}

.select2-container--default .dark-mode .select2-maroon .select2-selection--multiple .select2-selection__choice,
.dark-mode .select2-maroon .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #ed6c9b;
  border-color: #ea568c;
  color: #1f2d3d;
}

.select2-container--default .dark-mode .select2-maroon .select2-selection--multiple .select2-selection__choice__remove,
.dark-mode .select2-maroon .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  color: rgba(31, 45, 61, 0.7);
}

.select2-container--default .dark-mode .select2-maroon .select2-selection--multiple .select2-selection__choice__remove:hover,
.dark-mode .select2-maroon .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #1f2d3d;
}

.select2-container--default .dark-mode .select2-maroon.select2-container--focus .select2-selection--multiple,
.dark-mode .select2-maroon .select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #fbdee8;
}

.dark-mode .select2-blue + .select2-container--default.select2-container--open .select2-selection--single {
  border-color: #85a7ca;
}

.dark-mode .select2-blue + .select2-container--default.select2-container--focus .select2-selection--single {
  border-color: #85a7ca;
}

.select2-container--default .dark-mode .select2-blue.select2-dropdown .select2-search__field:focus,
.select2-container--default .dark-mode .select2-blue .select2-dropdown .select2-search__field:focus,
.select2-container--default .dark-mode .select2-blue .select2-search--inline .select2-search__field:focus,
.dark-mode .select2-blue .select2-container--default.select2-dropdown .select2-search__field:focus,
.dark-mode .select2-blue .select2-container--default .select2-dropdown .select2-search__field:focus,
.dark-mode .select2-blue .select2-container--default .select2-search--inline .select2-search__field:focus {
  border: 1px solid #85a7ca;
}

.select2-container--default .dark-mode .select2-blue .select2-results__option--highlighted,
.dark-mode .select2-blue .select2-container--default .select2-results__option--highlighted {
  background-color: #3f6791;
  color: #fff;
}

.select2-container--default .dark-mode .select2-blue .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-blue .select2-results__option--highlighted[aria-selected]:hover,
.dark-mode .select2-blue .select2-container--default .select2-results__option--highlighted[aria-selected],
.dark-mode .select2-blue .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {
  background-color: #3a5f86;
  color: #fff;
}

.select2-container--default .dark-mode .select2-blue .select2-selection--multiple:focus,
.dark-mode .select2-blue .select2-container--default .select2-selection--multiple:focus {
  border-color: #85a7ca;
}

.select2-container--default .dark-mode .select2-blue .select2-selection--multiple .select2-selection__choice,
.dark-mode .select2-blue .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #3f6791;
  border-color: #375a7f;
  color: #fff;
}

.select2-container--default .dark-mode .select2-blue .select2-selection--multiple .select2-selection__choice__remove,
.dark-mode .select2-blue .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  color: rgba(255, 255, 255, 0.7);
}

.select2-container--default .dark-mode .select2-blue .select2-selection--multiple .select2-selection__choice__remove:hover,
.dark-mode .select2-blue .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #fff;
}

.select2-container--default .dark-mode .select2-blue.select2-container--focus .select2-selection--multiple,
.dark-mode .select2-blue .select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #85a7ca;
}

.dark-mode .select2-indigo + .select2-container--default.select2-container--open .select2-selection--single {
  border-color: #b389f9;
}

.dark-mode .select2-indigo + .select2-container--default.select2-container--focus .select2-selection--single {
  border-color: #b389f9;
}

.select2-container--default .dark-mode .select2-indigo.select2-dropdown .select2-search__field:focus,
.select2-container--default .dark-mode .select2-indigo .select2-dropdown .select2-search__field:focus,
.select2-container--default .dark-mode .select2-indigo .select2-search--inline .select2-search__field:focus,
.dark-mode .select2-indigo .select2-container--default.select2-dropdown .select2-search__field:focus,
.dark-mode .select2-indigo .select2-container--default .select2-dropdown .select2-search__field:focus,
.dark-mode .select2-indigo .select2-container--default .select2-search--inline .select2-search__field:focus {
  border: 1px solid #b389f9;
}

.select2-container--default .dark-mode .select2-indigo .select2-results__option--highlighted,
.dark-mode .select2-indigo .select2-container--default .select2-results__option--highlighted {
  background-color: #6610f2;
  color: #fff;
}

.select2-container--default .dark-mode .select2-indigo .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-indigo .select2-results__option--highlighted[aria-selected]:hover,
.dark-mode .select2-indigo .select2-container--default .select2-results__option--highlighted[aria-selected],
.dark-mode .select2-indigo .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {
  background-color: #5f0de6;
  color: #fff;
}

.select2-container--default .dark-mode .select2-indigo .select2-selection--multiple:focus,
.dark-mode .select2-indigo .select2-container--default .select2-selection--multiple:focus {
  border-color: #b389f9;
}

.select2-container--default .dark-mode .select2-indigo .select2-selection--multiple .select2-selection__choice,
.dark-mode .select2-indigo .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #6610f2;
  border-color: #5b0cdd;
  color: #fff;
}

.select2-container--default .dark-mode .select2-indigo .select2-selection--multiple .select2-selection__choice__remove,
.dark-mode .select2-indigo .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  color: rgba(255, 255, 255, 0.7);
}

.select2-container--default .dark-mode .select2-indigo .select2-selection--multiple .select2-selection__choice__remove:hover,
.dark-mode .select2-indigo .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #fff;
}

.select2-container--default .dark-mode .select2-indigo.select2-container--focus .select2-selection--multiple,
.dark-mode .select2-indigo .select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #b389f9;
}

.dark-mode .select2-purple + .select2-container--default.select2-container--open .select2-selection--single {
  border-color: #b8a2e0;
}

.dark-mode .select2-purple + .select2-container--default.select2-container--focus .select2-selection--single {
  border-color: #b8a2e0;
}

.select2-container--default .dark-mode .select2-purple.select2-dropdown .select2-search__field:focus,
.select2-container--default .dark-mode .select2-purple .select2-dropdown .select2-search__field:focus,
.select2-container--default .dark-mode .select2-purple .select2-search--inline .select2-search__field:focus,
.dark-mode .select2-purple .select2-container--default.select2-dropdown .select2-search__field:focus,
.dark-mode .select2-purple .select2-container--default .select2-dropdown .select2-search__field:focus,
.dark-mode .select2-purple .select2-container--default .select2-search--inline .select2-search__field:focus {
  border: 1px solid #b8a2e0;
}

.select2-container--default .dark-mode .select2-purple .select2-results__option--highlighted,
.dark-mode .select2-purple .select2-container--default .select2-results__option--highlighted {
  background-color: #6f42c1;
  color: #fff;
}

.select2-container--default .dark-mode .select2-purple .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-purple .select2-results__option--highlighted[aria-selected]:hover,
.dark-mode .select2-purple .select2-container--default .select2-results__option--highlighted[aria-selected],
.dark-mode .select2-purple .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {
  background-color: #683cb8;
  color: #fff;
}

.select2-container--default .dark-mode .select2-purple .select2-selection--multiple:focus,
.dark-mode .select2-purple .select2-container--default .select2-selection--multiple:focus {
  border-color: #b8a2e0;
}

.select2-container--default .dark-mode .select2-purple .select2-selection--multiple .select2-selection__choice,
.dark-mode .select2-purple .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #6f42c1;
  border-color: #643ab0;
  color: #fff;
}

.select2-container--default .dark-mode .select2-purple .select2-selection--multiple .select2-selection__choice__remove,
.dark-mode .select2-purple .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  color: rgba(255, 255, 255, 0.7);
}

.select2-container--default .dark-mode .select2-purple .select2-selection--multiple .select2-selection__choice__remove:hover,
.dark-mode .select2-purple .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #fff;
}

.select2-container--default .dark-mode .select2-purple.select2-container--focus .select2-selection--multiple,
.dark-mode .select2-purple .select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #b8a2e0;
}

.dark-mode .select2-pink + .select2-container--default.select2-container--open .select2-selection--single {
  border-color: #f6b0d0;
}

.dark-mode .select2-pink + .select2-container--default.select2-container--focus .select2-selection--single {
  border-color: #f6b0d0;
}

.select2-container--default .dark-mode .select2-pink.select2-dropdown .select2-search__field:focus,
.select2-container--default .dark-mode .select2-pink .select2-dropdown .select2-search__field:focus,
.select2-container--default .dark-mode .select2-pink .select2-search--inline .select2-search__field:focus,
.dark-mode .select2-pink .select2-container--default.select2-dropdown .select2-search__field:focus,
.dark-mode .select2-pink .select2-container--default .select2-dropdown .select2-search__field:focus,
.dark-mode .select2-pink .select2-container--default .select2-search--inline .select2-search__field:focus {
  border: 1px solid #f6b0d0;
}

.select2-container--default .dark-mode .select2-pink .select2-results__option--highlighted,
.dark-mode .select2-pink .select2-container--default .select2-results__option--highlighted {
  background-color: #e83e8c;
  color: #fff;
}

.select2-container--default .dark-mode .select2-pink .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-pink .select2-results__option--highlighted[aria-selected]:hover,
.dark-mode .select2-pink .select2-container--default .select2-results__option--highlighted[aria-selected],
.dark-mode .select2-pink .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {
  background-color: #e63084;
  color: #fff;
}

.select2-container--default .dark-mode .select2-pink .select2-selection--multiple:focus,
.dark-mode .select2-pink .select2-container--default .select2-selection--multiple:focus {
  border-color: #f6b0d0;
}

.select2-container--default .dark-mode .select2-pink .select2-selection--multiple .select2-selection__choice,
.dark-mode .select2-pink .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #e83e8c;
  border-color: #e5277e;
  color: #fff;
}

.select2-container--default .dark-mode .select2-pink .select2-selection--multiple .select2-selection__choice__remove,
.dark-mode .select2-pink .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  color: rgba(255, 255, 255, 0.7);
}

.select2-container--default .dark-mode .select2-pink .select2-selection--multiple .select2-selection__choice__remove:hover,
.dark-mode .select2-pink .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #fff;
}

.select2-container--default .dark-mode .select2-pink.select2-container--focus .select2-selection--multiple,
.dark-mode .select2-pink .select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #f6b0d0;
}

.dark-mode .select2-red + .select2-container--default.select2-container--open .select2-selection--single {
  border-color: #f5b4ae;
}

.dark-mode .select2-red + .select2-container--default.select2-container--focus .select2-selection--single {
  border-color: #f5b4ae;
}

.select2-container--default .dark-mode .select2-red.select2-dropdown .select2-search__field:focus,
.select2-container--default .dark-mode .select2-red .select2-dropdown .select2-search__field:focus,
.select2-container--default .dark-mode .select2-red .select2-search--inline .select2-search__field:focus,
.dark-mode .select2-red .select2-container--default.select2-dropdown .select2-search__field:focus,
.dark-mode .select2-red .select2-container--default .select2-dropdown .select2-search__field:focus,
.dark-mode .select2-red .select2-container--default .select2-search--inline .select2-search__field:focus {
  border: 1px solid #f5b4ae;
}

.select2-container--default .dark-mode .select2-red .select2-results__option--highlighted,
.dark-mode .select2-red .select2-container--default .select2-results__option--highlighted {
  background-color: #e74c3c;
  color: #fff;
}

.select2-container--default .dark-mode .select2-red .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-red .select2-results__option--highlighted[aria-selected]:hover,
.dark-mode .select2-red .select2-container--default .select2-results__option--highlighted[aria-selected],
.dark-mode .select2-red .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {
  background-color: #e53f2e;
  color: #fff;
}

.select2-container--default .dark-mode .select2-red .select2-selection--multiple:focus,
.dark-mode .select2-red .select2-container--default .select2-selection--multiple:focus {
  border-color: #f5b4ae;
}

.select2-container--default .dark-mode .select2-red .select2-selection--multiple .select2-selection__choice,
.dark-mode .select2-red .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #e74c3c;
  border-color: #e43725;
  color: #fff;
}

.select2-container--default .dark-mode .select2-red .select2-selection--multiple .select2-selection__choice__remove,
.dark-mode .select2-red .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  color: rgba(255, 255, 255, 0.7);
}

.select2-container--default .dark-mode .select2-red .select2-selection--multiple .select2-selection__choice__remove:hover,
.dark-mode .select2-red .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #fff;
}

.select2-container--default .dark-mode .select2-red.select2-container--focus .select2-selection--multiple,
.dark-mode .select2-red .select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #f5b4ae;
}

.dark-mode .select2-orange + .select2-container--default.select2-container--open .select2-selection--single {
  border-color: #fec392;
}

.dark-mode .select2-orange + .select2-container--default.select2-container--focus .select2-selection--single {
  border-color: #fec392;
}

.select2-container--default .dark-mode .select2-orange.select2-dropdown .select2-search__field:focus,
.select2-container--default .dark-mode .select2-orange .select2-dropdown .select2-search__field:focus,
.select2-container--default .dark-mode .select2-orange .select2-search--inline .select2-search__field:focus,
.dark-mode .select2-orange .select2-container--default.select2-dropdown .select2-search__field:focus,
.dark-mode .select2-orange .select2-container--default .select2-dropdown .select2-search__field:focus,
.dark-mode .select2-orange .select2-container--default .select2-search--inline .select2-search__field:focus {
  border: 1px solid #fec392;
}

.select2-container--default .dark-mode .select2-orange .select2-results__option--highlighted,
.dark-mode .select2-orange .select2-container--default .select2-results__option--highlighted {
  background-color: #fd7e14;
  color: #1f2d3d;
}

.select2-container--default .dark-mode .select2-orange .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-orange .select2-results__option--highlighted[aria-selected]:hover,
.dark-mode .select2-orange .select2-container--default .select2-results__option--highlighted[aria-selected],
.dark-mode .select2-orange .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {
  background-color: #fd7605;
  color: #fff;
}

.select2-container--default .dark-mode .select2-orange .select2-selection--multiple:focus,
.dark-mode .select2-orange .select2-container--default .select2-selection--multiple:focus {
  border-color: #fec392;
}

.select2-container--default .dark-mode .select2-orange .select2-selection--multiple .select2-selection__choice,
.dark-mode .select2-orange .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #fd7e14;
  border-color: #f57102;
  color: #1f2d3d;
}

.select2-container--default .dark-mode .select2-orange .select2-selection--multiple .select2-selection__choice__remove,
.dark-mode .select2-orange .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  color: rgba(31, 45, 61, 0.7);
}

.select2-container--default .dark-mode .select2-orange .select2-selection--multiple .select2-selection__choice__remove:hover,
.dark-mode .select2-orange .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #1f2d3d;
}

.select2-container--default .dark-mode .select2-orange.select2-container--focus .select2-selection--multiple,
.dark-mode .select2-orange .select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #fec392;
}

.dark-mode .select2-yellow + .select2-container--default.select2-container--open .select2-selection--single {
  border-color: #f9cf8b;
}

.dark-mode .select2-yellow + .select2-container--default.select2-container--focus .select2-selection--single {
  border-color: #f9cf8b;
}

.select2-container--default .dark-mode .select2-yellow.select2-dropdown .select2-search__field:focus,
.select2-container--default .dark-mode .select2-yellow .select2-dropdown .select2-search__field:focus,
.select2-container--default .dark-mode .select2-yellow .select2-search--inline .select2-search__field:focus,
.dark-mode .select2-yellow .select2-container--default.select2-dropdown .select2-search__field:focus,
.dark-mode .select2-yellow .select2-container--default .select2-dropdown .select2-search__field:focus,
.dark-mode .select2-yellow .select2-container--default .select2-search--inline .select2-search__field:focus {
  border: 1px solid #f9cf8b;
}

.select2-container--default .dark-mode .select2-yellow .select2-results__option--highlighted,
.dark-mode .select2-yellow .select2-container--default .select2-results__option--highlighted {
  background-color: #f39c12;
  color: #1f2d3d;
}

.select2-container--default .dark-mode .select2-yellow .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-yellow .select2-results__option--highlighted[aria-selected]:hover,
.dark-mode .select2-yellow .select2-container--default .select2-results__option--highlighted[aria-selected],
.dark-mode .select2-yellow .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {
  background-color: #ea940c;
  color: #1f2d3d;
}

.select2-container--default .dark-mode .select2-yellow .select2-selection--multiple:focus,
.dark-mode .select2-yellow .select2-container--default .select2-selection--multiple:focus {
  border-color: #f9cf8b;
}

.select2-container--default .dark-mode .select2-yellow .select2-selection--multiple .select2-selection__choice,
.dark-mode .select2-yellow .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #f39c12;
  border-color: #e08e0b;
  color: #1f2d3d;
}

.select2-container--default .dark-mode .select2-yellow .select2-selection--multiple .select2-selection__choice__remove,
.dark-mode .select2-yellow .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  color: rgba(31, 45, 61, 0.7);
}

.select2-container--default .dark-mode .select2-yellow .select2-selection--multiple .select2-selection__choice__remove:hover,
.dark-mode .select2-yellow .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #1f2d3d;
}

.select2-container--default .dark-mode .select2-yellow.select2-container--focus .select2-selection--multiple,
.dark-mode .select2-yellow .select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #f9cf8b;
}

.dark-mode .select2-green + .select2-container--default.select2-container--open .select2-selection--single {
  border-color: #3dffcd;
}

.dark-mode .select2-green + .select2-container--default.select2-container--focus .select2-selection--single {
  border-color: #3dffcd;
}

.select2-container--default .dark-mode .select2-green.select2-dropdown .select2-search__field:focus,
.select2-container--default .dark-mode .select2-green .select2-dropdown .select2-search__field:focus,
.select2-container--default .dark-mode .select2-green .select2-search--inline .select2-search__field:focus,
.dark-mode .select2-green .select2-container--default.select2-dropdown .select2-search__field:focus,
.dark-mode .select2-green .select2-container--default .select2-dropdown .select2-search__field:focus,
.dark-mode .select2-green .select2-container--default .select2-search--inline .select2-search__field:focus {
  border: 1px solid #3dffcd;
}

.select2-container--default .dark-mode .select2-green .select2-results__option--highlighted,
.dark-mode .select2-green .select2-container--default .select2-results__option--highlighted {
  background-color: #00bc8c;
  color: #fff;
}

.select2-container--default .dark-mode .select2-green .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-green .select2-results__option--highlighted[aria-selected]:hover,
.dark-mode .select2-green .select2-container--default .select2-results__option--highlighted[aria-selected],
.dark-mode .select2-green .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {
  background-color: #00ad81;
  color: #fff;
}

.select2-container--default .dark-mode .select2-green .select2-selection--multiple:focus,
.dark-mode .select2-green .select2-container--default .select2-selection--multiple:focus {
  border-color: #3dffcd;
}

.select2-container--default .dark-mode .select2-green .select2-selection--multiple .select2-selection__choice,
.dark-mode .select2-green .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #00bc8c;
  border-color: #00a379;
  color: #fff;
}

.select2-container--default .dark-mode .select2-green .select2-selection--multiple .select2-selection__choice__remove,
.dark-mode .select2-green .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  color: rgba(255, 255, 255, 0.7);
}

.select2-container--default .dark-mode .select2-green .select2-selection--multiple .select2-selection__choice__remove:hover,
.dark-mode .select2-green .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #fff;
}

.select2-container--default .dark-mode .select2-green.select2-container--focus .select2-selection--multiple,
.dark-mode .select2-green .select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #3dffcd;
}

.dark-mode .select2-teal + .select2-container--default.select2-container--open .select2-selection--single {
  border-color: #7eeaca;
}

.dark-mode .select2-teal + .select2-container--default.select2-container--focus .select2-selection--single {
  border-color: #7eeaca;
}

.select2-container--default .dark-mode .select2-teal.select2-dropdown .select2-search__field:focus,
.select2-container--default .dark-mode .select2-teal .select2-dropdown .select2-search__field:focus,
.select2-container--default .dark-mode .select2-teal .select2-search--inline .select2-search__field:focus,
.dark-mode .select2-teal .select2-container--default.select2-dropdown .select2-search__field:focus,
.dark-mode .select2-teal .select2-container--default .select2-dropdown .select2-search__field:focus,
.dark-mode .select2-teal .select2-container--default .select2-search--inline .select2-search__field:focus {
  border: 1px solid #7eeaca;
}

.select2-container--default .dark-mode .select2-teal .select2-results__option--highlighted,
.dark-mode .select2-teal .select2-container--default .select2-results__option--highlighted {
  background-color: #20c997;
  color: #fff;
}

.select2-container--default .dark-mode .select2-teal .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-teal .select2-results__option--highlighted[aria-selected]:hover,
.dark-mode .select2-teal .select2-container--default .select2-results__option--highlighted[aria-selected],
.dark-mode .select2-teal .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {
  background-color: #1ebc8d;
  color: #fff;
}

.select2-container--default .dark-mode .select2-teal .select2-selection--multiple:focus,
.dark-mode .select2-teal .select2-container--default .select2-selection--multiple:focus {
  border-color: #7eeaca;
}

.select2-container--default .dark-mode .select2-teal .select2-selection--multiple .select2-selection__choice,
.dark-mode .select2-teal .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #20c997;
  border-color: #1cb386;
  color: #fff;
}

.select2-container--default .dark-mode .select2-teal .select2-selection--multiple .select2-selection__choice__remove,
.dark-mode .select2-teal .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  color: rgba(255, 255, 255, 0.7);
}

.select2-container--default .dark-mode .select2-teal .select2-selection--multiple .select2-selection__choice__remove:hover,
.dark-mode .select2-teal .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #fff;
}

.select2-container--default .dark-mode .select2-teal.select2-container--focus .select2-selection--multiple,
.dark-mode .select2-teal .select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #7eeaca;
}

.dark-mode .select2-cyan + .select2-container--default.select2-container--open .select2-selection--single {
  border-color: #a0cfee;
}

.dark-mode .select2-cyan + .select2-container--default.select2-container--focus .select2-selection--single {
  border-color: #a0cfee;
}

.select2-container--default .dark-mode .select2-cyan.select2-dropdown .select2-search__field:focus,
.select2-container--default .dark-mode .select2-cyan .select2-dropdown .select2-search__field:focus,
.select2-container--default .dark-mode .select2-cyan .select2-search--inline .select2-search__field:focus,
.dark-mode .select2-cyan .select2-container--default.select2-dropdown .select2-search__field:focus,
.dark-mode .select2-cyan .select2-container--default .select2-dropdown .select2-search__field:focus,
.dark-mode .select2-cyan .select2-container--default .select2-search--inline .select2-search__field:focus {
  border: 1px solid #a0cfee;
}

.select2-container--default .dark-mode .select2-cyan .select2-results__option--highlighted,
.dark-mode .select2-cyan .select2-container--default .select2-results__option--highlighted {
  background-color: #3498db;
  color: #fff;
}

.select2-container--default .dark-mode .select2-cyan .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-cyan .select2-results__option--highlighted[aria-selected]:hover,
.dark-mode .select2-cyan .select2-container--default .select2-results__option--highlighted[aria-selected],
.dark-mode .select2-cyan .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {
  background-color: #2791d9;
  color: #fff;
}

.select2-container--default .dark-mode .select2-cyan .select2-selection--multiple:focus,
.dark-mode .select2-cyan .select2-container--default .select2-selection--multiple:focus {
  border-color: #a0cfee;
}

.select2-container--default .dark-mode .select2-cyan .select2-selection--multiple .select2-selection__choice,
.dark-mode .select2-cyan .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #3498db;
  border-color: #258cd1;
  color: #fff;
}

.select2-container--default .dark-mode .select2-cyan .select2-selection--multiple .select2-selection__choice__remove,
.dark-mode .select2-cyan .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  color: rgba(255, 255, 255, 0.7);
}

.select2-container--default .dark-mode .select2-cyan .select2-selection--multiple .select2-selection__choice__remove:hover,
.dark-mode .select2-cyan .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #fff;
}

.select2-container--default .dark-mode .select2-cyan.select2-container--focus .select2-selection--multiple,
.dark-mode .select2-cyan .select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #a0cfee;
}

.dark-mode .select2-white + .select2-container--default.select2-container--open .select2-selection--single {
  border-color: white;
}

.dark-mode .select2-white + .select2-container--default.select2-container--focus .select2-selection--single {
  border-color: white;
}

.select2-container--default .dark-mode .select2-white.select2-dropdown .select2-search__field:focus,
.select2-container--default .dark-mode .select2-white .select2-dropdown .select2-search__field:focus,
.select2-container--default .dark-mode .select2-white .select2-search--inline .select2-search__field:focus,
.dark-mode .select2-white .select2-container--default.select2-dropdown .select2-search__field:focus,
.dark-mode .select2-white .select2-container--default .select2-dropdown .select2-search__field:focus,
.dark-mode .select2-white .select2-container--default .select2-search--inline .select2-search__field:focus {
  border: 1px solid white;
}

.select2-container--default .dark-mode .select2-white .select2-results__option--highlighted,
.dark-mode .select2-white .select2-container--default .select2-results__option--highlighted {
  background-color: #fff;
  color: #1f2d3d;
}

.select2-container--default .dark-mode .select2-white .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-white .select2-results__option--highlighted[aria-selected]:hover,
.dark-mode .select2-white .select2-container--default .select2-results__option--highlighted[aria-selected],
.dark-mode .select2-white .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {
  background-color: #f7f7f7;
  color: #1f2d3d;
}

.select2-container--default .dark-mode .select2-white .select2-selection--multiple:focus,
.dark-mode .select2-white .select2-container--default .select2-selection--multiple:focus {
  border-color: white;
}

.select2-container--default .dark-mode .select2-white .select2-selection--multiple .select2-selection__choice,
.dark-mode .select2-white .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #fff;
  border-color: #f2f2f2;
  color: #1f2d3d;
}

.select2-container--default .dark-mode .select2-white .select2-selection--multiple .select2-selection__choice__remove,
.dark-mode .select2-white .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  color: rgba(31, 45, 61, 0.7);
}

.select2-container--default .dark-mode .select2-white .select2-selection--multiple .select2-selection__choice__remove:hover,
.dark-mode .select2-white .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #1f2d3d;
}

.select2-container--default .dark-mode .select2-white.select2-container--focus .select2-selection--multiple,
.dark-mode .select2-white .select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: white;
}

.dark-mode .select2-gray + .select2-container--default.select2-container--open .select2-selection--single {
  border-color: #afb5ba;
}

.dark-mode .select2-gray + .select2-container--default.select2-container--focus .select2-selection--single {
  border-color: #afb5ba;
}

.select2-container--default .dark-mode .select2-gray.select2-dropdown .select2-search__field:focus,
.select2-container--default .dark-mode .select2-gray .select2-dropdown .select2-search__field:focus,
.select2-container--default .dark-mode .select2-gray .select2-search--inline .select2-search__field:focus,
.dark-mode .select2-gray .select2-container--default.select2-dropdown .select2-search__field:focus,
.dark-mode .select2-gray .select2-container--default .select2-dropdown .select2-search__field:focus,
.dark-mode .select2-gray .select2-container--default .select2-search--inline .select2-search__field:focus {
  border: 1px solid #afb5ba;
}

.select2-container--default .dark-mode .select2-gray .select2-results__option--highlighted,
.dark-mode .select2-gray .select2-container--default .select2-results__option--highlighted {
  background-color: #6c757d;
  color: #fff;
}

.select2-container--default .dark-mode .select2-gray .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-gray .select2-results__option--highlighted[aria-selected]:hover,
.dark-mode .select2-gray .select2-container--default .select2-results__option--highlighted[aria-selected],
.dark-mode .select2-gray .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {
  background-color: #656d75;
  color: #fff;
}

.select2-container--default .dark-mode .select2-gray .select2-selection--multiple:focus,
.dark-mode .select2-gray .select2-container--default .select2-selection--multiple:focus {
  border-color: #afb5ba;
}

.select2-container--default .dark-mode .select2-gray .select2-selection--multiple .select2-selection__choice,
.dark-mode .select2-gray .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #6c757d;
  border-color: #60686f;
  color: #fff;
}

.select2-container--default .dark-mode .select2-gray .select2-selection--multiple .select2-selection__choice__remove,
.dark-mode .select2-gray .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  color: rgba(255, 255, 255, 0.7);
}

.select2-container--default .dark-mode .select2-gray .select2-selection--multiple .select2-selection__choice__remove:hover,
.dark-mode .select2-gray .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #fff;
}

.select2-container--default .dark-mode .select2-gray.select2-container--focus .select2-selection--multiple,
.dark-mode .select2-gray .select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #afb5ba;
}

.dark-mode .select2-gray-dark + .select2-container--default.select2-container--open .select2-selection--single {
  border-color: #6d7a86;
}

.dark-mode .select2-gray-dark + .select2-container--default.select2-container--focus .select2-selection--single {
  border-color: #6d7a86;
}

.select2-container--default .dark-mode .select2-gray-dark.select2-dropdown .select2-search__field:focus,
.select2-container--default .dark-mode .select2-gray-dark .select2-dropdown .select2-search__field:focus,
.select2-container--default .dark-mode .select2-gray-dark .select2-search--inline .select2-search__field:focus,
.dark-mode .select2-gray-dark .select2-container--default.select2-dropdown .select2-search__field:focus,
.dark-mode .select2-gray-dark .select2-container--default .select2-dropdown .select2-search__field:focus,
.dark-mode .select2-gray-dark .select2-container--default .select2-search--inline .select2-search__field:focus {
  border: 1px solid #6d7a86;
}

.select2-container--default .dark-mode .select2-gray-dark .select2-results__option--highlighted,
.dark-mode .select2-gray-dark .select2-container--default .select2-results__option--highlighted {
  background-color: #343a40;
  color: #fff;
}

.select2-container--default .dark-mode .select2-gray-dark .select2-results__option--highlighted[aria-selected], .select2-container--default .dark-mode .select2-gray-dark .select2-results__option--highlighted[aria-selected]:hover,
.dark-mode .select2-gray-dark .select2-container--default .select2-results__option--highlighted[aria-selected],
.dark-mode .select2-gray-dark .select2-container--default .select2-results__option--highlighted[aria-selected]:hover {
  background-color: #2d3238;
  color: #fff;
}

.select2-container--default .dark-mode .select2-gray-dark .select2-selection--multiple:focus,
.dark-mode .select2-gray-dark .select2-container--default .select2-selection--multiple:focus {
  border-color: #6d7a86;
}

.select2-container--default .dark-mode .select2-gray-dark .select2-selection--multiple .select2-selection__choice,
.dark-mode .select2-gray-dark .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #343a40;
  border-color: #292d32;
  color: #fff;
}

.select2-container--default .dark-mode .select2-gray-dark .select2-selection--multiple .select2-selection__choice__remove,
.dark-mode .select2-gray-dark .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  color: rgba(255, 255, 255, 0.7);
}

.select2-container--default .dark-mode .select2-gray-dark .select2-selection--multiple .select2-selection__choice__remove:hover,
.dark-mode .select2-gray-dark .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #fff;
}

.select2-container--default .dark-mode .select2-gray-dark.select2-container--focus .select2-selection--multiple,
.dark-mode .select2-gray-dark .select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #6d7a86;
}

.slider .tooltip.in {
  opacity: 0.9;
}

.slider.slider-vertical {
  height: 100%;
}

.slider.slider-horizontal {
  width: 100%;
}

.slider-primary .slider .slider-selection {
  background: #007bff;
}

.slider-secondary .slider .slider-selection {
  background: #6c757d;
}

.slider-success .slider .slider-selection {
  background: #28a745;
}

.slider-info .slider .slider-selection {
  background: #17a2b8;
}

.slider-warning .slider .slider-selection {
  background: #ffc107;
}

.slider-danger .slider .slider-selection {
  background: #dc3545;
}

.slider-light .slider .slider-selection {
  background: #f8f9fa;
}

.slider-dark .slider .slider-selection {
  background: #343a40;
}

.slider-lightblue .slider .slider-selection {
  background: #3c8dbc;
}

.slider-navy .slider .slider-selection {
  background: #001f3f;
}

.slider-olive .slider .slider-selection {
  background: #3d9970;
}

.slider-lime .slider .slider-selection {
  background: #01ff70;
}

.slider-fuchsia .slider .slider-selection {
  background: #f012be;
}

.slider-maroon .slider .slider-selection {
  background: #d81b60;
}

.slider-blue .slider .slider-selection {
  background: #007bff;
}

.slider-indigo .slider .slider-selection {
  background: #6610f2;
}

.slider-purple .slider .slider-selection {
  background: #6f42c1;
}

.slider-pink .slider .slider-selection {
  background: #e83e8c;
}

.slider-red .slider .slider-selection {
  background: #dc3545;
}

.slider-orange .slider .slider-selection {
  background: #fd7e14;
}

.slider-yellow .slider .slider-selection {
  background: #ffc107;
}

.slider-green .slider .slider-selection {
  background: #28a745;
}

.slider-teal .slider .slider-selection {
  background: #20c997;
}

.slider-cyan .slider .slider-selection {
  background: #17a2b8;
}

.slider-white .slider .slider-selection {
  background: #fff;
}

.slider-gray .slider .slider-selection {
  background: #6c757d;
}

.slider-gray-dark .slider .slider-selection {
  background: #343a40;
}

.dark-mode .slider-track {
  background-color: #4b545c;
  background-image: none;
}

.dark-mode .slider-primary .slider .slider-selection {
  background: #3f6791;
}

.dark-mode .slider-secondary .slider .slider-selection {
  background: #6c757d;
}

.dark-mode .slider-success .slider .slider-selection {
  background: #00bc8c;
}

.dark-mode .slider-info .slider .slider-selection {
  background: #3498db;
}

.dark-mode .slider-warning .slider .slider-selection {
  background: #f39c12;
}

.dark-mode .slider-danger .slider .slider-selection {
  background: #e74c3c;
}

.dark-mode .slider-light .slider .slider-selection {
  background: #f8f9fa;
}

.dark-mode .slider-dark .slider .slider-selection {
  background: #343a40;
}

.dark-mode .slider-lightblue .slider .slider-selection {
  background: #86bad8;
}

.dark-mode .slider-navy .slider .slider-selection {
  background: #002c59;
}

.dark-mode .slider-olive .slider .slider-selection {
  background: #74c8a3;
}

.dark-mode .slider-lime .slider .slider-selection {
  background: #67ffa9;
}

.dark-mode .slider-fuchsia .slider .slider-selection {
  background: #f672d8;
}

.dark-mode .slider-maroon .slider .slider-selection {
  background: #ed6c9b;
}

.dark-mode .slider-blue .slider .slider-selection {
  background: #3f6791;
}

.dark-mode .slider-indigo .slider .slider-selection {
  background: #6610f2;
}

.dark-mode .slider-purple .slider .slider-selection {
  background: #6f42c1;
}

.dark-mode .slider-pink .slider .slider-selection {
  background: #e83e8c;
}

.dark-mode .slider-red .slider .slider-selection {
  background: #e74c3c;
}

.dark-mode .slider-orange .slider .slider-selection {
  background: #fd7e14;
}

.dark-mode .slider-yellow .slider .slider-selection {
  background: #f39c12;
}

.dark-mode .slider-green .slider .slider-selection {
  background: #00bc8c;
}

.dark-mode .slider-teal .slider .slider-selection {
  background: #20c997;
}

.dark-mode .slider-cyan .slider .slider-selection {
  background: #3498db;
}

.dark-mode .slider-white .slider .slider-selection {
  background: #fff;
}

.dark-mode .slider-gray .slider .slider-selection {
  background: #6c757d;
}

.dark-mode .slider-gray-dark .slider .slider-selection {
  background: #343a40;
}

.icheck-primary > input:first-child:not(:checked):not(:disabled):hover + label::before,
.icheck-primary > input:first-child:not(:checked):not(:disabled):hover + input[type="hidden"] + label::before {
  border-color: #007bff;
}

.icheck-primary > input:first-child:not(:checked):not(:disabled):focus + label::before,
.icheck-primary > input:first-child:not(:checked):not(:disabled):focus + input[type="hidden"] + label::before {
  border-color: #007bff;
}

.icheck-primary > input:first-child:checked + label::before,
.icheck-primary > input:first-child:checked + input[type="hidden"] + label::before {
  background-color: #007bff;
  border-color: #007bff;
}

.icheck-secondary > input:first-child:not(:checked):not(:disabled):hover + label::before,
.icheck-secondary > input:first-child:not(:checked):not(:disabled):hover + input[type="hidden"] + label::before {
  border-color: #6c757d;
}

.icheck-secondary > input:first-child:not(:checked):not(:disabled):focus + label::before,
.icheck-secondary > input:first-child:not(:checked):not(:disabled):focus + input[type="hidden"] + label::before {
  border-color: #6c757d;
}

.icheck-secondary > input:first-child:checked + label::before,
.icheck-secondary > input:first-child:checked + input[type="hidden"] + label::before {
  background-color: #6c757d;
  border-color: #6c757d;
}

.icheck-success > input:first-child:not(:checked):not(:disabled):hover + label::before,
.icheck-success > input:first-child:not(:checked):not(:disabled):hover + input[type="hidden"] + label::before {
  border-color: #28a745;
}

.icheck-success > input:first-child:not(:checked):not(:disabled):focus + label::before,
.icheck-success > input:first-child:not(:checked):not(:disabled):focus + input[type="hidden"] + label::before {
  border-color: #28a745;
}

.icheck-success > input:first-child:checked + label::before,
.icheck-success > input:first-child:checked + input[type="hidden"] + label::before {
  background-color: #28a745;
  border-color: #28a745;
}

.icheck-info > input:first-child:not(:checked):not(:disabled):hover + label::before,
.icheck-info > input:first-child:not(:checked):not(:disabled):hover + input[type="hidden"] + label::before {
  border-color: #17a2b8;
}

.icheck-info > input:first-child:not(:checked):not(:disabled):focus + label::before,
.icheck-info > input:first-child:not(:checked):not(:disabled):focus + input[type="hidden"] + label::before {
  border-color: #17a2b8;
}

.icheck-info > input:first-child:checked + label::before,
.icheck-info > input:first-child:checked + input[type="hidden"] + label::before {
  background-color: #17a2b8;
  border-color: #17a2b8;
}

.icheck-warning > input:first-child:not(:checked):not(:disabled):hover + label::before,
.icheck-warning > input:first-child:not(:checked):not(:disabled):hover + input[type="hidden"] + label::before {
  border-color: #ffc107;
}

.icheck-warning > input:first-child:not(:checked):not(:disabled):focus + label::before,
.icheck-warning > input:first-child:not(:checked):not(:disabled):focus + input[type="hidden"] + label::before {
  border-color: #ffc107;
}

.icheck-warning > input:first-child:checked + label::before,
.icheck-warning > input:first-child:checked + input[type="hidden"] + label::before {
  background-color: #ffc107;
  border-color: #ffc107;
}

.icheck-danger > input:first-child:not(:checked):not(:disabled):hover + label::before,
.icheck-danger > input:first-child:not(:checked):not(:disabled):hover + input[type="hidden"] + label::before {
  border-color: #dc3545;
}

.icheck-danger > input:first-child:not(:checked):not(:disabled):focus + label::before,
.icheck-danger > input:first-child:not(:checked):not(:disabled):focus + input[type="hidden"] + label::before {
  border-color: #dc3545;
}

.icheck-danger > input:first-child:checked + label::before,
.icheck-danger > input:first-child:checked + input[type="hidden"] + label::before {
  background-color: #dc3545;
  border-color: #dc3545;
}

.icheck-light > input:first-child:not(:checked):not(:disabled):hover + label::before,
.icheck-light > input:first-child:not(:checked):not(:disabled):hover + input[type="hidden"] + label::before {
  border-color: #f8f9fa;
}

.icheck-light > input:first-child:not(:checked):not(:disabled):focus + label::before,
.icheck-light > input:first-child:not(:checked):not(:disabled):focus + input[type="hidden"] + label::before {
  border-color: #f8f9fa;
}

.icheck-light > input:first-child:checked + label::before,
.icheck-light > input:first-child:checked + input[type="hidden"] + label::before {
  background-color: #f8f9fa;
  border-color: #f8f9fa;
}

.icheck-dark > input:first-child:not(:checked):not(:disabled):hover + label::before,
.icheck-dark > input:first-child:not(:checked):not(:disabled):hover + input[type="hidden"] + label::before {
  border-color: #343a40;
}

.icheck-dark > input:first-child:not(:checked):not(:disabled):focus + label::before,
.icheck-dark > input:first-child:not(:checked):not(:disabled):focus + input[type="hidden"] + label::before {
  border-color: #343a40;
}

.icheck-dark > input:first-child:checked + label::before,
.icheck-dark > input:first-child:checked + input[type="hidden"] + label::before {
  background-color: #343a40;
  border-color: #343a40;
}

.icheck-lightblue > input:first-child:not(:checked):not(:disabled):hover + label::before,
.icheck-lightblue > input:first-child:not(:checked):not(:disabled):hover + input[type="hidden"] + label::before {
  border-color: #3c8dbc;
}

.icheck-lightblue > input:first-child:not(:checked):not(:disabled):focus + label::before,
.icheck-lightblue > input:first-child:not(:checked):not(:disabled):focus + input[type="hidden"] + label::before {
  border-color: #3c8dbc;
}

.icheck-lightblue > input:first-child:checked + label::before,
.icheck-lightblue > input:first-child:checked + input[type="hidden"] + label::before {
  background-color: #3c8dbc;
  border-color: #3c8dbc;
}

.icheck-navy > input:first-child:not(:checked):not(:disabled):hover + label::before,
.icheck-navy > input:first-child:not(:checked):not(:disabled):hover + input[type="hidden"] + label::before {
  border-color: #001f3f;
}

.icheck-navy > input:first-child:not(:checked):not(:disabled):focus + label::before,
.icheck-navy > input:first-child:not(:checked):not(:disabled):focus + input[type="hidden"] + label::before {
  border-color: #001f3f;
}

.icheck-navy > input:first-child:checked + label::before,
.icheck-navy > input:first-child:checked + input[type="hidden"] + label::before {
  background-color: #001f3f;
  border-color: #001f3f;
}

.icheck-olive > input:first-child:not(:checked):not(:disabled):hover + label::before,
.icheck-olive > input:first-child:not(:checked):not(:disabled):hover + input[type="hidden"] + label::before {
  border-color: #3d9970;
}

.icheck-olive > input:first-child:not(:checked):not(:disabled):focus + label::before,
.icheck-olive > input:first-child:not(:checked):not(:disabled):focus + input[type="hidden"] + label::before {
  border-color: #3d9970;
}

.icheck-olive > input:first-child:checked + label::before,
.icheck-olive > input:first-child:checked + input[type="hidden"] + label::before {
  background-color: #3d9970;
  border-color: #3d9970;
}

.icheck-lime > input:first-child:not(:checked):not(:disabled):hover + label::before,
.icheck-lime > input:first-child:not(:checked):not(:disabled):hover + input[type="hidden"] + label::before {
  border-color: #01ff70;
}

.icheck-lime > input:first-child:not(:checked):not(:disabled):focus + label::before,
.icheck-lime > input:first-child:not(:checked):not(:disabled):focus + input[type="hidden"] + label::before {
  border-color: #01ff70;
}

.icheck-lime > input:first-child:checked + label::before,
.icheck-lime > input:first-child:checked + input[type="hidden"] + label::before {
  background-color: #01ff70;
  border-color: #01ff70;
}

.icheck-fuchsia > input:first-child:not(:checked):not(:disabled):hover + label::before,
.icheck-fuchsia > input:first-child:not(:checked):not(:disabled):hover + input[type="hidden"] + label::before {
  border-color: #f012be;
}

.icheck-fuchsia > input:first-child:not(:checked):not(:disabled):focus + label::before,
.icheck-fuchsia > input:first-child:not(:checked):not(:disabled):focus + input[type="hidden"] + label::before {
  border-color: #f012be;
}

.icheck-fuchsia > input:first-child:checked + label::before,
.icheck-fuchsia > input:first-child:checked + input[type="hidden"] + label::before {
  background-color: #f012be;
  border-color: #f012be;
}

.icheck-maroon > input:first-child:not(:checked):not(:disabled):hover + label::before,
.icheck-maroon > input:first-child:not(:checked):not(:disabled):hover + input[type="hidden"] + label::before {
  border-color: #d81b60;
}

.icheck-maroon > input:first-child:not(:checked):not(:disabled):focus + label::before,
.icheck-maroon > input:first-child:not(:checked):not(:disabled):focus + input[type="hidden"] + label::before {
  border-color: #d81b60;
}

.icheck-maroon > input:first-child:checked + label::before,
.icheck-maroon > input:first-child:checked + input[type="hidden"] + label::before {
  background-color: #d81b60;
  border-color: #d81b60;
}

.icheck-blue > input:first-child:not(:checked):not(:disabled):hover + label::before,
.icheck-blue > input:first-child:not(:checked):not(:disabled):hover + input[type="hidden"] + label::before {
  border-color: #007bff;
}

.icheck-blue > input:first-child:not(:checked):not(:disabled):focus + label::before,
.icheck-blue > input:first-child:not(:checked):not(:disabled):focus + input[type="hidden"] + label::before {
  border-color: #007bff;
}

.icheck-blue > input:first-child:checked + label::before,
.icheck-blue > input:first-child:checked + input[type="hidden"] + label::before {
  background-color: #007bff;
  border-color: #007bff;
}

.icheck-indigo > input:first-child:not(:checked):not(:disabled):hover + label::before,
.icheck-indigo > input:first-child:not(:checked):not(:disabled):hover + input[type="hidden"] + label::before {
  border-color: #6610f2;
}

.icheck-indigo > input:first-child:not(:checked):not(:disabled):focus + label::before,
.icheck-indigo > input:first-child:not(:checked):not(:disabled):focus + input[type="hidden"] + label::before {
  border-color: #6610f2;
}

.icheck-indigo > input:first-child:checked + label::before,
.icheck-indigo > input:first-child:checked + input[type="hidden"] + label::before {
  background-color: #6610f2;
  border-color: #6610f2;
}

.icheck-purple > input:first-child:not(:checked):not(:disabled):hover + label::before,
.icheck-purple > input:first-child:not(:checked):not(:disabled):hover + input[type="hidden"] + label::before {
  border-color: #6f42c1;
}

.icheck-purple > input:first-child:not(:checked):not(:disabled):focus + label::before,
.icheck-purple > input:first-child:not(:checked):not(:disabled):focus + input[type="hidden"] + label::before {
  border-color: #6f42c1;
}

.icheck-purple > input:first-child:checked + label::before,
.icheck-purple > input:first-child:checked + input[type="hidden"] + label::before {
  background-color: #6f42c1;
  border-color: #6f42c1;
}

.icheck-pink > input:first-child:not(:checked):not(:disabled):hover + label::before,
.icheck-pink > input:first-child:not(:checked):not(:disabled):hover + input[type="hidden"] + label::before {
  border-color: #e83e8c;
}

.icheck-pink > input:first-child:not(:checked):not(:disabled):focus + label::before,
.icheck-pink > input:first-child:not(:checked):not(:disabled):focus + input[type="hidden"] + label::before {
  border-color: #e83e8c;
}

.icheck-pink > input:first-child:checked + label::before,
.icheck-pink > input:first-child:checked + input[type="hidden"] + label::before {
  background-color: #e83e8c;
  border-color: #e83e8c;
}

.icheck-red > input:first-child:not(:checked):not(:disabled):hover + label::before,
.icheck-red > input:first-child:not(:checked):not(:disabled):hover + input[type="hidden"] + label::before {
  border-color: #dc3545;
}

.icheck-red > input:first-child:not(:checked):not(:disabled):focus + label::before,
.icheck-red > input:first-child:not(:checked):not(:disabled):focus + input[type="hidden"] + label::before {
  border-color: #dc3545;
}

.icheck-red > input:first-child:checked + label::before,
.icheck-red > input:first-child:checked + input[type="hidden"] + label::before {
  background-color: #dc3545;
  border-color: #dc3545;
}

.icheck-orange > input:first-child:not(:checked):not(:disabled):hover + label::before,
.icheck-orange > input:first-child:not(:checked):not(:disabled):hover + input[type="hidden"] + label::before {
  border-color: #fd7e14;
}

.icheck-orange > input:first-child:not(:checked):not(:disabled):focus + label::before,
.icheck-orange > input:first-child:not(:checked):not(:disabled):focus + input[type="hidden"] + label::before {
  border-color: #fd7e14;
}

.icheck-orange > input:first-child:checked + label::before,
.icheck-orange > input:first-child:checked + input[type="hidden"] + label::before {
  background-color: #fd7e14;
  border-color: #fd7e14;
}

.icheck-yellow > input:first-child:not(:checked):not(:disabled):hover + label::before,
.icheck-yellow > input:first-child:not(:checked):not(:disabled):hover + input[type="hidden"] + label::before {
  border-color: #ffc107;
}

.icheck-yellow > input:first-child:not(:checked):not(:disabled):focus + label::before,
.icheck-yellow > input:first-child:not(:checked):not(:disabled):focus + input[type="hidden"] + label::before {
  border-color: #ffc107;
}

.icheck-yellow > input:first-child:checked + label::before,
.icheck-yellow > input:first-child:checked + input[type="hidden"] + label::before {
  background-color: #ffc107;
  border-color: #ffc107;
}

.icheck-green > input:first-child:not(:checked):not(:disabled):hover + label::before,
.icheck-green > input:first-child:not(:checked):not(:disabled):hover + input[type="hidden"] + label::before {
  border-color: #28a745;
}

.icheck-green > input:first-child:not(:checked):not(:disabled):focus + label::before,
.icheck-green > input:first-child:not(:checked):not(:disabled):focus + input[type="hidden"] + label::before {
  border-color: #28a745;
}

.icheck-green > input:first-child:checked + label::before,
.icheck-green > input:first-child:checked + input[type="hidden"] + label::before {
  background-color: #28a745;
  border-color: #28a745;
}

.icheck-teal > input:first-child:not(:checked):not(:disabled):hover + label::before,
.icheck-teal > input:first-child:not(:checked):not(:disabled):hover + input[type="hidden"] + label::before {
  border-color: #20c997;
}

.icheck-teal > input:first-child:not(:checked):not(:disabled):focus + label::before,
.icheck-teal > input:first-child:not(:checked):not(:disabled):focus + input[type="hidden"] + label::before {
  border-color: #20c997;
}

.icheck-teal > input:first-child:checked + label::before,
.icheck-teal > input:first-child:checked + input[type="hidden"] + label::before {
  background-color: #20c997;
  border-color: #20c997;
}

.icheck-cyan > input:first-child:not(:checked):not(:disabled):hover + label::before,
.icheck-cyan > input:first-child:not(:checked):not(:disabled):hover + input[type="hidden"] + label::before {
  border-color: #17a2b8;
}

.icheck-cyan > input:first-child:not(:checked):not(:disabled):focus + label::before,
.icheck-cyan > input:first-child:not(:checked):not(:disabled):focus + input[type="hidden"] + label::before {
  border-color: #17a2b8;
}

.icheck-cyan > input:first-child:checked + label::before,
.icheck-cyan > input:first-child:checked + input[type="hidden"] + label::before {
  background-color: #17a2b8;
  border-color: #17a2b8;
}

.icheck-white > input:first-child:not(:checked):not(:disabled):hover + label::before,
.icheck-white > input:first-child:not(:checked):not(:disabled):hover + input[type="hidden"] + label::before {
  border-color: #fff;
}

.icheck-white > input:first-child:not(:checked):not(:disabled):focus + label::before,
.icheck-white > input:first-child:not(:checked):not(:disabled):focus + input[type="hidden"] + label::before {
  border-color: #fff;
}

.icheck-white > input:first-child:checked + label::before,
.icheck-white > input:first-child:checked + input[type="hidden"] + label::before {
  background-color: #fff;
  border-color: #fff;
}

.icheck-gray > input:first-child:not(:checked):not(:disabled):hover + label::before,
.icheck-gray > input:first-child:not(:checked):not(:disabled):hover + input[type="hidden"] + label::before {
  border-color: #6c757d;
}

.icheck-gray > input:first-child:not(:checked):not(:disabled):focus + label::before,
.icheck-gray > input:first-child:not(:checked):not(:disabled):focus + input[type="hidden"] + label::before {
  border-color: #6c757d;
}

.icheck-gray > input:first-child:checked + label::before,
.icheck-gray > input:first-child:checked + input[type="hidden"] + label::before {
  background-color: #6c757d;
  border-color: #6c757d;
}

.icheck-gray-dark > input:first-child:not(:checked):not(:disabled):hover + label::before,
.icheck-gray-dark > input:first-child:not(:checked):not(:disabled):hover + input[type="hidden"] + label::before {
  border-color: #343a40;
}

.icheck-gray-dark > input:first-child:not(:checked):not(:disabled):focus + label::before,
.icheck-gray-dark > input:first-child:not(:checked):not(:disabled):focus + input[type="hidden"] + label::before {
  border-color: #343a40;
}

.icheck-gray-dark > input:first-child:checked + label::before,
.icheck-gray-dark > input:first-child:checked + input[type="hidden"] + label::before {
  background-color: #343a40;
  border-color: #343a40;
}

.dark-mode [class*="icheck-"] > input:first-child:not(:checked) + input[type="hidden"] + label::before,
.dark-mode [class*="icheck-"] > input:first-child:not(:checked) + label::before {
  border-color: #6c757d;
}

.dark-mode .icheck-primary > input:first-child:not(:checked):not(:disabled):hover + label::before,
.dark-mode .icheck-primary > input:first-child:not(:checked):not(:disabled):hover + input[type="hidden"] + label::before {
  border-color: #3f6791;
}

.dark-mode .icheck-primary > input:first-child:not(:checked):not(:disabled):focus + label::before,
.dark-mode .icheck-primary > input:first-child:not(:checked):not(:disabled):focus + input[type="hidden"] + label::before {
  border-color: #3f6791;
}

.dark-mode .icheck-primary > input:first-child:checked + label::before,
.dark-mode .icheck-primary > input:first-child:checked + input[type="hidden"] + label::before {
  background-color: #3f6791;
  border-color: #3f6791;
}

.dark-mode .icheck-secondary > input:first-child:not(:checked):not(:disabled):hover + label::before,
.dark-mode .icheck-secondary > input:first-child:not(:checked):not(:disabled):hover + input[type="hidden"] + label::before {
  border-color: #6c757d;
}

.dark-mode .icheck-secondary > input:first-child:not(:checked):not(:disabled):focus + label::before,
.dark-mode .icheck-secondary > input:first-child:not(:checked):not(:disabled):focus + input[type="hidden"] + label::before {
  border-color: #6c757d;
}

.dark-mode .icheck-secondary > input:first-child:checked + label::before,
.dark-mode .icheck-secondary > input:first-child:checked + input[type="hidden"] + label::before {
  background-color: #6c757d;
  border-color: #6c757d;
}

.dark-mode .icheck-success > input:first-child:not(:checked):not(:disabled):hover + label::before,
.dark-mode .icheck-success > input:first-child:not(:checked):not(:disabled):hover + input[type="hidden"] + label::before {
  border-color: #00bc8c;
}

.dark-mode .icheck-success > input:first-child:not(:checked):not(:disabled):focus + label::before,
.dark-mode .icheck-success > input:first-child:not(:checked):not(:disabled):focus + input[type="hidden"] + label::before {
  border-color: #00bc8c;
}

.dark-mode .icheck-success > input:first-child:checked + label::before,
.dark-mode .icheck-success > input:first-child:checked + input[type="hidden"] + label::before {
  background-color: #00bc8c;
  border-color: #00bc8c;
}

.dark-mode .icheck-info > input:first-child:not(:checked):not(:disabled):hover + label::before,
.dark-mode .icheck-info > input:first-child:not(:checked):not(:disabled):hover + input[type="hidden"] + label::before {
  border-color: #3498db;
}

.dark-mode .icheck-info > input:first-child:not(:checked):not(:disabled):focus + label::before,
.dark-mode .icheck-info > input:first-child:not(:checked):not(:disabled):focus + input[type="hidden"] + label::before {
  border-color: #3498db;
}

.dark-mode .icheck-info > input:first-child:checked + label::before,
.dark-mode .icheck-info > input:first-child:checked + input[type="hidden"] + label::before {
  background-color: #3498db;
  border-color: #3498db;
}

.dark-mode .icheck-warning > input:first-child:not(:checked):not(:disabled):hover + label::before,
.dark-mode .icheck-warning > input:first-child:not(:checked):not(:disabled):hover + input[type="hidden"] + label::before {
  border-color: #f39c12;
}

.dark-mode .icheck-warning > input:first-child:not(:checked):not(:disabled):focus + label::before,
.dark-mode .icheck-warning > input:first-child:not(:checked):not(:disabled):focus + input[type="hidden"] + label::before {
  border-color: #f39c12;
}

.dark-mode .icheck-warning > input:first-child:checked + label::before,
.dark-mode .icheck-warning > input:first-child:checked + input[type="hidden"] + label::before {
  background-color: #f39c12;
  border-color: #f39c12;
}

.dark-mode .icheck-danger > input:first-child:not(:checked):not(:disabled):hover + label::before,
.dark-mode .icheck-danger > input:first-child:not(:checked):not(:disabled):hover + input[type="hidden"] + label::before {
  border-color: #e74c3c;
}

.dark-mode .icheck-danger > input:first-child:not(:checked):not(:disabled):focus + label::before,
.dark-mode .icheck-danger > input:first-child:not(:checked):not(:disabled):focus + input[type="hidden"] + label::before {
  border-color: #e74c3c;
}

.dark-mode .icheck-danger > input:first-child:checked + label::before,
.dark-mode .icheck-danger > input:first-child:checked + input[type="hidden"] + label::before {
  background-color: #e74c3c;
  border-color: #e74c3c;
}

.dark-mode .icheck-light > input:first-child:not(:checked):not(:disabled):hover + label::before,
.dark-mode .icheck-light > input:first-child:not(:checked):not(:disabled):hover + input[type="hidden"] + label::before {
  border-color: #f8f9fa;
}

.dark-mode .icheck-light > input:first-child:not(:checked):not(:disabled):focus + label::before,
.dark-mode .icheck-light > input:first-child:not(:checked):not(:disabled):focus + input[type="hidden"] + label::before {
  border-color: #f8f9fa;
}

.dark-mode .icheck-light > input:first-child:checked + label::before,
.dark-mode .icheck-light > input:first-child:checked + input[type="hidden"] + label::before {
  background-color: #f8f9fa;
  border-color: #f8f9fa;
}

.dark-mode .icheck-dark > input:first-child:not(:checked):not(:disabled):hover + label::before,
.dark-mode .icheck-dark > input:first-child:not(:checked):not(:disabled):hover + input[type="hidden"] + label::before {
  border-color: #343a40;
}

.dark-mode .icheck-dark > input:first-child:not(:checked):not(:disabled):focus + label::before,
.dark-mode .icheck-dark > input:first-child:not(:checked):not(:disabled):focus + input[type="hidden"] + label::before {
  border-color: #343a40;
}

.dark-mode .icheck-dark > input:first-child:checked + label::before,
.dark-mode .icheck-dark > input:first-child:checked + input[type="hidden"] + label::before {
  background-color: #343a40;
  border-color: #343a40;
}

.dark-mode .icheck-lightblue > input:first-child:not(:checked):not(:disabled):hover + label::before,
.dark-mode .icheck-lightblue > input:first-child:not(:checked):not(:disabled):hover + input[type="hidden"] + label::before {
  border-color: #86bad8;
}

.dark-mode .icheck-lightblue > input:first-child:not(:checked):not(:disabled):focus + label::before,
.dark-mode .icheck-lightblue > input:first-child:not(:checked):not(:disabled):focus + input[type="hidden"] + label::before {
  border-color: #86bad8;
}

.dark-mode .icheck-lightblue > input:first-child:checked + label::before,
.dark-mode .icheck-lightblue > input:first-child:checked + input[type="hidden"] + label::before {
  background-color: #86bad8;
  border-color: #86bad8;
}

.dark-mode .icheck-navy > input:first-child:not(:checked):not(:disabled):hover + label::before,
.dark-mode .icheck-navy > input:first-child:not(:checked):not(:disabled):hover + input[type="hidden"] + label::before {
  border-color: #002c59;
}

.dark-mode .icheck-navy > input:first-child:not(:checked):not(:disabled):focus + label::before,
.dark-mode .icheck-navy > input:first-child:not(:checked):not(:disabled):focus + input[type="hidden"] + label::before {
  border-color: #002c59;
}

.dark-mode .icheck-navy > input:first-child:checked + label::before,
.dark-mode .icheck-navy > input:first-child:checked + input[type="hidden"] + label::before {
  background-color: #002c59;
  border-color: #002c59;
}

.dark-mode .icheck-olive > input:first-child:not(:checked):not(:disabled):hover + label::before,
.dark-mode .icheck-olive > input:first-child:not(:checked):not(:disabled):hover + input[type="hidden"] + label::before {
  border-color: #74c8a3;
}

.dark-mode .icheck-olive > input:first-child:not(:checked):not(:disabled):focus + label::before,
.dark-mode .icheck-olive > input:first-child:not(:checked):not(:disabled):focus + input[type="hidden"] + label::before {
  border-color: #74c8a3;
}

.dark-mode .icheck-olive > input:first-child:checked + label::before,
.dark-mode .icheck-olive > input:first-child:checked + input[type="hidden"] + label::before {
  background-color: #74c8a3;
  border-color: #74c8a3;
}

.dark-mode .icheck-lime > input:first-child:not(:checked):not(:disabled):hover + label::before,
.dark-mode .icheck-lime > input:first-child:not(:checked):not(:disabled):hover + input[type="hidden"] + label::before {
  border-color: #67ffa9;
}

.dark-mode .icheck-lime > input:first-child:not(:checked):not(:disabled):focus + label::before,
.dark-mode .icheck-lime > input:first-child:not(:checked):not(:disabled):focus + input[type="hidden"] + label::before {
  border-color: #67ffa9;
}

.dark-mode .icheck-lime > input:first-child:checked + label::before,
.dark-mode .icheck-lime > input:first-child:checked + input[type="hidden"] + label::before {
  background-color: #67ffa9;
  border-color: #67ffa9;
}

.dark-mode .icheck-fuchsia > input:first-child:not(:checked):not(:disabled):hover + label::before,
.dark-mode .icheck-fuchsia > input:first-child:not(:checked):not(:disabled):hover + input[type="hidden"] + label::before {
  border-color: #f672d8;
}

.dark-mode .icheck-fuchsia > input:first-child:not(:checked):not(:disabled):focus + label::before,
.dark-mode .icheck-fuchsia > input:first-child:not(:checked):not(:disabled):focus + input[type="hidden"] + label::before {
  border-color: #f672d8;
}

.dark-mode .icheck-fuchsia > input:first-child:checked + label::before,
.dark-mode .icheck-fuchsia > input:first-child:checked + input[type="hidden"] + label::before {
  background-color: #f672d8;
  border-color: #f672d8;
}

.dark-mode .icheck-maroon > input:first-child:not(:checked):not(:disabled):hover + label::before,
.dark-mode .icheck-maroon > input:first-child:not(:checked):not(:disabled):hover + input[type="hidden"] + label::before {
  border-color: #ed6c9b;
}

.dark-mode .icheck-maroon > input:first-child:not(:checked):not(:disabled):focus + label::before,
.dark-mode .icheck-maroon > input:first-child:not(:checked):not(:disabled):focus + input[type="hidden"] + label::before {
  border-color: #ed6c9b;
}

.dark-mode .icheck-maroon > input:first-child:checked + label::before,
.dark-mode .icheck-maroon > input:first-child:checked + input[type="hidden"] + label::before {
  background-color: #ed6c9b;
  border-color: #ed6c9b;
}

.dark-mode .icheck-blue > input:first-child:not(:checked):not(:disabled):hover + label::before,
.dark-mode .icheck-blue > input:first-child:not(:checked):not(:disabled):hover + input[type="hidden"] + label::before {
  border-color: #3f6791;
}

.dark-mode .icheck-blue > input:first-child:not(:checked):not(:disabled):focus + label::before,
.dark-mode .icheck-blue > input:first-child:not(:checked):not(:disabled):focus + input[type="hidden"] + label::before {
  border-color: #3f6791;
}

.dark-mode .icheck-blue > input:first-child:checked + label::before,
.dark-mode .icheck-blue > input:first-child:checked + input[type="hidden"] + label::before {
  background-color: #3f6791;
  border-color: #3f6791;
}

.dark-mode .icheck-indigo > input:first-child:not(:checked):not(:disabled):hover + label::before,
.dark-mode .icheck-indigo > input:first-child:not(:checked):not(:disabled):hover + input[type="hidden"] + label::before {
  border-color: #6610f2;
}

.dark-mode .icheck-indigo > input:first-child:not(:checked):not(:disabled):focus + label::before,
.dark-mode .icheck-indigo > input:first-child:not(:checked):not(:disabled):focus + input[type="hidden"] + label::before {
  border-color: #6610f2;
}

.dark-mode .icheck-indigo > input:first-child:checked + label::before,
.dark-mode .icheck-indigo > input:first-child:checked + input[type="hidden"] + label::before {
  background-color: #6610f2;
  border-color: #6610f2;
}

.dark-mode .icheck-purple > input:first-child:not(:checked):not(:disabled):hover + label::before,
.dark-mode .icheck-purple > input:first-child:not(:checked):not(:disabled):hover + input[type="hidden"] + label::before {
  border-color: #6f42c1;
}

.dark-mode .icheck-purple > input:first-child:not(:checked):not(:disabled):focus + label::before,
.dark-mode .icheck-purple > input:first-child:not(:checked):not(:disabled):focus + input[type="hidden"] + label::before {
  border-color: #6f42c1;
}

.dark-mode .icheck-purple > input:first-child:checked + label::before,
.dark-mode .icheck-purple > input:first-child:checked + input[type="hidden"] + label::before {
  background-color: #6f42c1;
  border-color: #6f42c1;
}

.dark-mode .icheck-pink > input:first-child:not(:checked):not(:disabled):hover + label::before,
.dark-mode .icheck-pink > input:first-child:not(:checked):not(:disabled):hover + input[type="hidden"] + label::before {
  border-color: #e83e8c;
}

.dark-mode .icheck-pink > input:first-child:not(:checked):not(:disabled):focus + label::before,
.dark-mode .icheck-pink > input:first-child:not(:checked):not(:disabled):focus + input[type="hidden"] + label::before {
  border-color: #e83e8c;
}

.dark-mode .icheck-pink > input:first-child:checked + label::before,
.dark-mode .icheck-pink > input:first-child:checked + input[type="hidden"] + label::before {
  background-color: #e83e8c;
  border-color: #e83e8c;
}

.dark-mode .icheck-red > input:first-child:not(:checked):not(:disabled):hover + label::before,
.dark-mode .icheck-red > input:first-child:not(:checked):not(:disabled):hover + input[type="hidden"] + label::before {
  border-color: #e74c3c;
}

.dark-mode .icheck-red > input:first-child:not(:checked):not(:disabled):focus + label::before,
.dark-mode .icheck-red > input:first-child:not(:checked):not(:disabled):focus + input[type="hidden"] + label::before {
  border-color: #e74c3c;
}

.dark-mode .icheck-red > input:first-child:checked + label::before,
.dark-mode .icheck-red > input:first-child:checked + input[type="hidden"] + label::before {
  background-color: #e74c3c;
  border-color: #e74c3c;
}

.dark-mode .icheck-orange > input:first-child:not(:checked):not(:disabled):hover + label::before,
.dark-mode .icheck-orange > input:first-child:not(:checked):not(:disabled):hover + input[type="hidden"] + label::before {
  border-color: #fd7e14;
}

.dark-mode .icheck-orange > input:first-child:not(:checked):not(:disabled):focus + label::before,
.dark-mode .icheck-orange > input:first-child:not(:checked):not(:disabled):focus + input[type="hidden"] + label::before {
  border-color: #fd7e14;
}

.dark-mode .icheck-orange > input:first-child:checked + label::before,
.dark-mode .icheck-orange > input:first-child:checked + input[type="hidden"] + label::before {
  background-color: #fd7e14;
  border-color: #fd7e14;
}

.dark-mode .icheck-yellow > input:first-child:not(:checked):not(:disabled):hover + label::before,
.dark-mode .icheck-yellow > input:first-child:not(:checked):not(:disabled):hover + input[type="hidden"] + label::before {
  border-color: #f39c12;
}

.dark-mode .icheck-yellow > input:first-child:not(:checked):not(:disabled):focus + label::before,
.dark-mode .icheck-yellow > input:first-child:not(:checked):not(:disabled):focus + input[type="hidden"] + label::before {
  border-color: #f39c12;
}

.dark-mode .icheck-yellow > input:first-child:checked + label::before,
.dark-mode .icheck-yellow > input:first-child:checked + input[type="hidden"] + label::before {
  background-color: #f39c12;
  border-color: #f39c12;
}

.dark-mode .icheck-green > input:first-child:not(:checked):not(:disabled):hover + label::before,
.dark-mode .icheck-green > input:first-child:not(:checked):not(:disabled):hover + input[type="hidden"] + label::before {
  border-color: #00bc8c;
}

.dark-mode .icheck-green > input:first-child:not(:checked):not(:disabled):focus + label::before,
.dark-mode .icheck-green > input:first-child:not(:checked):not(:disabled):focus + input[type="hidden"] + label::before {
  border-color: #00bc8c;
}

.dark-mode .icheck-green > input:first-child:checked + label::before,
.dark-mode .icheck-green > input:first-child:checked + input[type="hidden"] + label::before {
  background-color: #00bc8c;
  border-color: #00bc8c;
}

.dark-mode .icheck-teal > input:first-child:not(:checked):not(:disabled):hover + label::before,
.dark-mode .icheck-teal > input:first-child:not(:checked):not(:disabled):hover + input[type="hidden"] + label::before {
  border-color: #20c997;
}

.dark-mode .icheck-teal > input:first-child:not(:checked):not(:disabled):focus + label::before,
.dark-mode .icheck-teal > input:first-child:not(:checked):not(:disabled):focus + input[type="hidden"] + label::before {
  border-color: #20c997;
}

.dark-mode .icheck-teal > input:first-child:checked + label::before,
.dark-mode .icheck-teal > input:first-child:checked + input[type="hidden"] + label::before {
  background-color: #20c997;
  border-color: #20c997;
}

.dark-mode .icheck-cyan > input:first-child:not(:checked):not(:disabled):hover + label::before,
.dark-mode .icheck-cyan > input:first-child:not(:checked):not(:disabled):hover + input[type="hidden"] + label::before {
  border-color: #3498db;
}

.dark-mode .icheck-cyan > input:first-child:not(:checked):not(:disabled):focus + label::before,
.dark-mode .icheck-cyan > input:first-child:not(:checked):not(:disabled):focus + input[type="hidden"] + label::before {
  border-color: #3498db;
}

.dark-mode .icheck-cyan > input:first-child:checked + label::before,
.dark-mode .icheck-cyan > input:first-child:checked + input[type="hidden"] + label::before {
  background-color: #3498db;
  border-color: #3498db;
}

.dark-mode .icheck-white > input:first-child:not(:checked):not(:disabled):hover + label::before,
.dark-mode .icheck-white > input:first-child:not(:checked):not(:disabled):hover + input[type="hidden"] + label::before {
  border-color: #fff;
}

.dark-mode .icheck-white > input:first-child:not(:checked):not(:disabled):focus + label::before,
.dark-mode .icheck-white > input:first-child:not(:checked):not(:disabled):focus + input[type="hidden"] + label::before {
  border-color: #fff;
}

.dark-mode .icheck-white > input:first-child:checked + label::before,
.dark-mode .icheck-white > input:first-child:checked + input[type="hidden"] + label::before {
  background-color: #fff;
  border-color: #fff;
}

.dark-mode .icheck-gray > input:first-child:not(:checked):not(:disabled):hover + label::before,
.dark-mode .icheck-gray > input:first-child:not(:checked):not(:disabled):hover + input[type="hidden"] + label::before {
  border-color: #6c757d;
}

.dark-mode .icheck-gray > input:first-child:not(:checked):not(:disabled):focus + label::before,
.dark-mode .icheck-gray > input:first-child:not(:checked):not(:disabled):focus + input[type="hidden"] + label::before {
  border-color: #6c757d;
}

.dark-mode .icheck-gray > input:first-child:checked + label::before,
.dark-mode .icheck-gray > input:first-child:checked + input[type="hidden"] + label::before {
  background-color: #6c757d;
  border-color: #6c757d;
}

.dark-mode .icheck-gray-dark > input:first-child:not(:checked):not(:disabled):hover + label::before,
.dark-mode .icheck-gray-dark > input:first-child:not(:checked):not(:disabled):hover + input[type="hidden"] + label::before {
  border-color: #343a40;
}

.dark-mode .icheck-gray-dark > input:first-child:not(:checked):not(:disabled):focus + label::before,
.dark-mode .icheck-gray-dark > input:first-child:not(:checked):not(:disabled):focus + input[type="hidden"] + label::before {
  border-color: #343a40;
}

.dark-mode .icheck-gray-dark > input:first-child:checked + label::before,
.dark-mode .icheck-gray-dark > input:first-child:checked + input[type="hidden"] + label::before {
  background-color: #343a40;
  border-color: #343a40;
}

.mapael .map {
  position: relative;
}

.mapael .mapTooltip {
  font-family: "Source Sans Pro", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
  font-style: normal;
  font-weight: 400;
  line-height: 1.5;
  text-align: left;
  text-align: start;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  letter-spacing: normal;
  word-break: normal;
  word-spacing: normal;
  white-space: normal;
  line-break: auto;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  background-color: #000;
  color: #fff;
  display: block;
  max-width: 200px;
  padding: 0.25rem 0.5rem;
  position: absolute;
  text-align: center;
  word-wrap: break-word;
  z-index: 1070;
}

.mapael .myLegend {
  background-color: #f8f9fa;
  border: 1px solid #adb5bd;
  padding: 10px;
  width: 600px;
}

.mapael .zoomButton {
  background-color: #f8f9fa;
  border: 1px solid #ddd;
  border-radius: 0.25rem;
  color: #444;
  cursor: pointer;
  font-weight: 700;
  height: 16px;
  left: 10px;
  line-height: 14px;
  padding-left: 1px;
  position: absolute;
  text-align: center;
  top: 0;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: 16px;
}

.mapael .zoomButton:hover, .mapael .zoomButton:active, .mapael .zoomButton.hover {
  background-color: #e9ecef;
  color: #2b2b2b;
}

.mapael .zoomReset {
  line-height: 12px;
  top: 10px;
}

.mapael .zoomIn {
  top: 30px;
}

.mapael .zoomOut {
  top: 50px;
}

.jqvmap-zoomin,
.jqvmap-zoomout {
  background-color: #f8f9fa;
  border: 1px solid #ddd;
  border-radius: 0.25rem;
  color: #444;
  height: 15px;
  width: 15px;
  padding: 1px 2px;
}

.jqvmap-zoomin:hover, .jqvmap-zoomin:active, .jqvmap-zoomin.hover,
.jqvmap-zoomout:hover,
.jqvmap-zoomout:active,
.jqvmap-zoomout.hover {
  background-color: #e9ecef;
  color: #2b2b2b;
}

.swal2-icon.swal2-info {
  border-color: ligthen(#17a2b8, 20%);
  color: #17a2b8;
}

.swal2-icon.swal2-warning {
  border-color: ligthen(#ffc107, 20%);
  color: #ffc107;
}

.swal2-icon.swal2-error {
  border-color: ligthen(#dc3545, 20%);
  color: #dc3545;
}

.swal2-icon.swal2-question {
  border-color: ligthen(#6c757d, 20%);
  color: #6c757d;
}

.swal2-icon.swal2-success {
  border-color: ligthen(#28a745, 20%);
  color: #28a745;
}

.swal2-icon.swal2-success .swal2-success-ring {
  border-color: ligthen(#28a745, 20%);
}

.swal2-icon.swal2-success [class^='swal2-success-line'] {
  background-color: #28a745;
}

.dark-mode .swal2-popup {
  background-color: #343a40;
  color: #e9ecef;
}

.dark-mode .swal2-popup .swal2-content,
.dark-mode .swal2-popup .swal2-title {
  color: #e9ecef;
}

#toast-container .toast {
  background-color: #007bff;
}

#toast-container .toast-success {
  background-color: #28a745;
}

#toast-container .toast-error {
  background-color: #dc3545;
}

#toast-container .toast-info {
  background-color: #17a2b8;
}

#toast-container .toast-warning {
  background-color: #ffc107;
}

.toast-bottom-full-width .toast,
.toast-top-full-width .toast {
  max-width: inherit;
}

.pace {
  z-index: 1048;
}

.pace .pace-progress {
  z-index: 1049;
}

.pace .pace-activity {
  z-index: 1050;
}

.pace-primary .pace .pace-progress {
  background: #007bff;
}

.pace-barber-shop-primary .pace {
  background: #fff;
}

.pace-barber-shop-primary .pace .pace-progress {
  background: #007bff;
}

.pace-barber-shop-primary .pace .pace-activity {
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);
}

.pace-big-counter-primary .pace .pace-progress::after {
  color: rgba(0, 123, 255, 0.2);
}

.pace-bounce-primary .pace .pace-activity {
  background: #007bff;
}

.pace-center-atom-primary .pace-progress {
  height: 100px;
  width: 80px;
}

.pace-center-atom-primary .pace-progress::before {
  background: #007bff;
  color: #fff;
  font-size: .8rem;
  line-height: .7rem;
  padding-top: 17%;
}

.pace-center-atom-primary .pace-activity {
  border-color: #007bff;
}

.pace-center-atom-primary .pace-activity::after, .pace-center-atom-primary .pace-activity::before {
  border-color: #007bff;
}

.pace-center-circle-primary .pace .pace-progress {
  background: rgba(0, 123, 255, 0.8);
  color: #fff;
}

.pace-center-radar-primary .pace .pace-activity {
  border-color: #007bff transparent transparent;
}

.pace-center-radar-primary .pace .pace-activity::before {
  border-color: #007bff transparent transparent;
}

.pace-center-simple-primary .pace {
  background: #fff;
  border-color: #007bff;
}

.pace-center-simple-primary .pace .pace-progress {
  background: #007bff;
}

.pace-material-primary .pace {
  color: #007bff;
}

.pace-corner-indicator-primary .pace .pace-activity {
  background: #007bff;
}

.pace-corner-indicator-primary .pace .pace-activity::after,
.pace-corner-indicator-primary .pace .pace-activity::before {
  border: 5px solid #fff;
}

.pace-corner-indicator-primary .pace .pace-activity::before {
  border-right-color: rgba(0, 123, 255, 0.2);
  border-left-color: rgba(0, 123, 255, 0.2);
}

.pace-corner-indicator-primary .pace .pace-activity::after {
  border-top-color: rgba(0, 123, 255, 0.2);
  border-bottom-color: rgba(0, 123, 255, 0.2);
}

.pace-fill-left-primary .pace .pace-progress {
  background-color: rgba(0, 123, 255, 0.2);
}

.pace-flash-primary .pace .pace-progress {
  background: #007bff;
}

.pace-flash-primary .pace .pace-progress-inner {
  box-shadow: 0 0 10px #007bff, 0 0 5px #007bff;
}

.pace-flash-primary .pace .pace-activity {
  border-top-color: #007bff;
  border-left-color: #007bff;
}

.pace-loading-bar-primary .pace .pace-progress {
  background: #007bff;
  color: #007bff;
  box-shadow: 120px 0 #fff, 240px 0 #fff;
}

.pace-loading-bar-primary .pace .pace-activity {
  box-shadow: inset 0 0 0 2px #007bff, inset 0 0 0 7px #fff;
}

.pace-mac-osx-primary .pace .pace-progress {
  background-color: #007bff;
  box-shadow: inset -1px 0 #007bff, inset 0 -1px #007bff, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);
}

.pace-mac-osx-primary .pace .pace-activity {
  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);
  height: 12px;
}

.pace-progress-color-primary .pace-progress {
  color: #007bff;
}

.pace-secondary .pace .pace-progress {
  background: #6c757d;
}

.pace-barber-shop-secondary .pace {
  background: #fff;
}

.pace-barber-shop-secondary .pace .pace-progress {
  background: #6c757d;
}

.pace-barber-shop-secondary .pace .pace-activity {
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);
}

.pace-big-counter-secondary .pace .pace-progress::after {
  color: rgba(108, 117, 125, 0.2);
}

.pace-bounce-secondary .pace .pace-activity {
  background: #6c757d;
}

.pace-center-atom-secondary .pace-progress {
  height: 100px;
  width: 80px;
}

.pace-center-atom-secondary .pace-progress::before {
  background: #6c757d;
  color: #fff;
  font-size: .8rem;
  line-height: .7rem;
  padding-top: 17%;
}

.pace-center-atom-secondary .pace-activity {
  border-color: #6c757d;
}

.pace-center-atom-secondary .pace-activity::after, .pace-center-atom-secondary .pace-activity::before {
  border-color: #6c757d;
}

.pace-center-circle-secondary .pace .pace-progress {
  background: rgba(108, 117, 125, 0.8);
  color: #fff;
}

.pace-center-radar-secondary .pace .pace-activity {
  border-color: #6c757d transparent transparent;
}

.pace-center-radar-secondary .pace .pace-activity::before {
  border-color: #6c757d transparent transparent;
}

.pace-center-simple-secondary .pace {
  background: #fff;
  border-color: #6c757d;
}

.pace-center-simple-secondary .pace .pace-progress {
  background: #6c757d;
}

.pace-material-secondary .pace {
  color: #6c757d;
}

.pace-corner-indicator-secondary .pace .pace-activity {
  background: #6c757d;
}

.pace-corner-indicator-secondary .pace .pace-activity::after,
.pace-corner-indicator-secondary .pace .pace-activity::before {
  border: 5px solid #fff;
}

.pace-corner-indicator-secondary .pace .pace-activity::before {
  border-right-color: rgba(108, 117, 125, 0.2);
  border-left-color: rgba(108, 117, 125, 0.2);
}

.pace-corner-indicator-secondary .pace .pace-activity::after {
  border-top-color: rgba(108, 117, 125, 0.2);
  border-bottom-color: rgba(108, 117, 125, 0.2);
}

.pace-fill-left-secondary .pace .pace-progress {
  background-color: rgba(108, 117, 125, 0.2);
}

.pace-flash-secondary .pace .pace-progress {
  background: #6c757d;
}

.pace-flash-secondary .pace .pace-progress-inner {
  box-shadow: 0 0 10px #6c757d, 0 0 5px #6c757d;
}

.pace-flash-secondary .pace .pace-activity {
  border-top-color: #6c757d;
  border-left-color: #6c757d;
}

.pace-loading-bar-secondary .pace .pace-progress {
  background: #6c757d;
  color: #6c757d;
  box-shadow: 120px 0 #fff, 240px 0 #fff;
}

.pace-loading-bar-secondary .pace .pace-activity {
  box-shadow: inset 0 0 0 2px #6c757d, inset 0 0 0 7px #fff;
}

.pace-mac-osx-secondary .pace .pace-progress {
  background-color: #6c757d;
  box-shadow: inset -1px 0 #6c757d, inset 0 -1px #6c757d, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);
}

.pace-mac-osx-secondary .pace .pace-activity {
  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);
  height: 12px;
}

.pace-progress-color-secondary .pace-progress {
  color: #6c757d;
}

.pace-success .pace .pace-progress {
  background: #28a745;
}

.pace-barber-shop-success .pace {
  background: #fff;
}

.pace-barber-shop-success .pace .pace-progress {
  background: #28a745;
}

.pace-barber-shop-success .pace .pace-activity {
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);
}

.pace-big-counter-success .pace .pace-progress::after {
  color: rgba(40, 167, 69, 0.2);
}

.pace-bounce-success .pace .pace-activity {
  background: #28a745;
}

.pace-center-atom-success .pace-progress {
  height: 100px;
  width: 80px;
}

.pace-center-atom-success .pace-progress::before {
  background: #28a745;
  color: #fff;
  font-size: .8rem;
  line-height: .7rem;
  padding-top: 17%;
}

.pace-center-atom-success .pace-activity {
  border-color: #28a745;
}

.pace-center-atom-success .pace-activity::after, .pace-center-atom-success .pace-activity::before {
  border-color: #28a745;
}

.pace-center-circle-success .pace .pace-progress {
  background: rgba(40, 167, 69, 0.8);
  color: #fff;
}

.pace-center-radar-success .pace .pace-activity {
  border-color: #28a745 transparent transparent;
}

.pace-center-radar-success .pace .pace-activity::before {
  border-color: #28a745 transparent transparent;
}

.pace-center-simple-success .pace {
  background: #fff;
  border-color: #28a745;
}

.pace-center-simple-success .pace .pace-progress {
  background: #28a745;
}

.pace-material-success .pace {
  color: #28a745;
}

.pace-corner-indicator-success .pace .pace-activity {
  background: #28a745;
}

.pace-corner-indicator-success .pace .pace-activity::after,
.pace-corner-indicator-success .pace .pace-activity::before {
  border: 5px solid #fff;
}

.pace-corner-indicator-success .pace .pace-activity::before {
  border-right-color: rgba(40, 167, 69, 0.2);
  border-left-color: rgba(40, 167, 69, 0.2);
}

.pace-corner-indicator-success .pace .pace-activity::after {
  border-top-color: rgba(40, 167, 69, 0.2);
  border-bottom-color: rgba(40, 167, 69, 0.2);
}

.pace-fill-left-success .pace .pace-progress {
  background-color: rgba(40, 167, 69, 0.2);
}

.pace-flash-success .pace .pace-progress {
  background: #28a745;
}

.pace-flash-success .pace .pace-progress-inner {
  box-shadow: 0 0 10px #28a745, 0 0 5px #28a745;
}

.pace-flash-success .pace .pace-activity {
  border-top-color: #28a745;
  border-left-color: #28a745;
}

.pace-loading-bar-success .pace .pace-progress {
  background: #28a745;
  color: #28a745;
  box-shadow: 120px 0 #fff, 240px 0 #fff;
}

.pace-loading-bar-success .pace .pace-activity {
  box-shadow: inset 0 0 0 2px #28a745, inset 0 0 0 7px #fff;
}

.pace-mac-osx-success .pace .pace-progress {
  background-color: #28a745;
  box-shadow: inset -1px 0 #28a745, inset 0 -1px #28a745, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);
}

.pace-mac-osx-success .pace .pace-activity {
  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);
  height: 12px;
}

.pace-progress-color-success .pace-progress {
  color: #28a745;
}

.pace-info .pace .pace-progress {
  background: #17a2b8;
}

.pace-barber-shop-info .pace {
  background: #fff;
}

.pace-barber-shop-info .pace .pace-progress {
  background: #17a2b8;
}

.pace-barber-shop-info .pace .pace-activity {
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);
}

.pace-big-counter-info .pace .pace-progress::after {
  color: rgba(23, 162, 184, 0.2);
}

.pace-bounce-info .pace .pace-activity {
  background: #17a2b8;
}

.pace-center-atom-info .pace-progress {
  height: 100px;
  width: 80px;
}

.pace-center-atom-info .pace-progress::before {
  background: #17a2b8;
  color: #fff;
  font-size: .8rem;
  line-height: .7rem;
  padding-top: 17%;
}

.pace-center-atom-info .pace-activity {
  border-color: #17a2b8;
}

.pace-center-atom-info .pace-activity::after, .pace-center-atom-info .pace-activity::before {
  border-color: #17a2b8;
}

.pace-center-circle-info .pace .pace-progress {
  background: rgba(23, 162, 184, 0.8);
  color: #fff;
}

.pace-center-radar-info .pace .pace-activity {
  border-color: #17a2b8 transparent transparent;
}

.pace-center-radar-info .pace .pace-activity::before {
  border-color: #17a2b8 transparent transparent;
}

.pace-center-simple-info .pace {
  background: #fff;
  border-color: #17a2b8;
}

.pace-center-simple-info .pace .pace-progress {
  background: #17a2b8;
}

.pace-material-info .pace {
  color: #17a2b8;
}

.pace-corner-indicator-info .pace .pace-activity {
  background: #17a2b8;
}

.pace-corner-indicator-info .pace .pace-activity::after,
.pace-corner-indicator-info .pace .pace-activity::before {
  border: 5px solid #fff;
}

.pace-corner-indicator-info .pace .pace-activity::before {
  border-right-color: rgba(23, 162, 184, 0.2);
  border-left-color: rgba(23, 162, 184, 0.2);
}

.pace-corner-indicator-info .pace .pace-activity::after {
  border-top-color: rgba(23, 162, 184, 0.2);
  border-bottom-color: rgba(23, 162, 184, 0.2);
}

.pace-fill-left-info .pace .pace-progress {
  background-color: rgba(23, 162, 184, 0.2);
}

.pace-flash-info .pace .pace-progress {
  background: #17a2b8;
}

.pace-flash-info .pace .pace-progress-inner {
  box-shadow: 0 0 10px #17a2b8, 0 0 5px #17a2b8;
}

.pace-flash-info .pace .pace-activity {
  border-top-color: #17a2b8;
  border-left-color: #17a2b8;
}

.pace-loading-bar-info .pace .pace-progress {
  background: #17a2b8;
  color: #17a2b8;
  box-shadow: 120px 0 #fff, 240px 0 #fff;
}

.pace-loading-bar-info .pace .pace-activity {
  box-shadow: inset 0 0 0 2px #17a2b8, inset 0 0 0 7px #fff;
}

.pace-mac-osx-info .pace .pace-progress {
  background-color: #17a2b8;
  box-shadow: inset -1px 0 #17a2b8, inset 0 -1px #17a2b8, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);
}

.pace-mac-osx-info .pace .pace-activity {
  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);
  height: 12px;
}

.pace-progress-color-info .pace-progress {
  color: #17a2b8;
}

.pace-warning .pace .pace-progress {
  background: #ffc107;
}

.pace-barber-shop-warning .pace {
  background: #1f2d3d;
}

.pace-barber-shop-warning .pace .pace-progress {
  background: #ffc107;
}

.pace-barber-shop-warning .pace .pace-activity {
  background-image: linear-gradient(45deg, rgba(31, 45, 61, 0.2) 25%, transparent 25%, transparent 50%, rgba(31, 45, 61, 0.2) 50%, rgba(31, 45, 61, 0.2) 75%, transparent 75%, transparent);
}

.pace-big-counter-warning .pace .pace-progress::after {
  color: rgba(255, 193, 7, 0.2);
}

.pace-bounce-warning .pace .pace-activity {
  background: #ffc107;
}

.pace-center-atom-warning .pace-progress {
  height: 100px;
  width: 80px;
}

.pace-center-atom-warning .pace-progress::before {
  background: #ffc107;
  color: #1f2d3d;
  font-size: .8rem;
  line-height: .7rem;
  padding-top: 17%;
}

.pace-center-atom-warning .pace-activity {
  border-color: #ffc107;
}

.pace-center-atom-warning .pace-activity::after, .pace-center-atom-warning .pace-activity::before {
  border-color: #ffc107;
}

.pace-center-circle-warning .pace .pace-progress {
  background: rgba(255, 193, 7, 0.8);
  color: #1f2d3d;
}

.pace-center-radar-warning .pace .pace-activity {
  border-color: #ffc107 transparent transparent;
}

.pace-center-radar-warning .pace .pace-activity::before {
  border-color: #ffc107 transparent transparent;
}

.pace-center-simple-warning .pace {
  background: #1f2d3d;
  border-color: #ffc107;
}

.pace-center-simple-warning .pace .pace-progress {
  background: #ffc107;
}

.pace-material-warning .pace {
  color: #ffc107;
}

.pace-corner-indicator-warning .pace .pace-activity {
  background: #ffc107;
}

.pace-corner-indicator-warning .pace .pace-activity::after,
.pace-corner-indicator-warning .pace .pace-activity::before {
  border: 5px solid #1f2d3d;
}

.pace-corner-indicator-warning .pace .pace-activity::before {
  border-right-color: rgba(255, 193, 7, 0.2);
  border-left-color: rgba(255, 193, 7, 0.2);
}

.pace-corner-indicator-warning .pace .pace-activity::after {
  border-top-color: rgba(255, 193, 7, 0.2);
  border-bottom-color: rgba(255, 193, 7, 0.2);
}

.pace-fill-left-warning .pace .pace-progress {
  background-color: rgba(255, 193, 7, 0.2);
}

.pace-flash-warning .pace .pace-progress {
  background: #ffc107;
}

.pace-flash-warning .pace .pace-progress-inner {
  box-shadow: 0 0 10px #ffc107, 0 0 5px #ffc107;
}

.pace-flash-warning .pace .pace-activity {
  border-top-color: #ffc107;
  border-left-color: #ffc107;
}

.pace-loading-bar-warning .pace .pace-progress {
  background: #ffc107;
  color: #ffc107;
  box-shadow: 120px 0 #1f2d3d, 240px 0 #1f2d3d;
}

.pace-loading-bar-warning .pace .pace-activity {
  box-shadow: inset 0 0 0 2px #ffc107, inset 0 0 0 7px #1f2d3d;
}

.pace-mac-osx-warning .pace .pace-progress {
  background-color: #ffc107;
  box-shadow: inset -1px 0 #ffc107, inset 0 -1px #ffc107, inset 0 2px rgba(31, 45, 61, 0.5), inset 0 6px rgba(31, 45, 61, 0.3);
}

.pace-mac-osx-warning .pace .pace-activity {
  background-image: radial-gradient(rgba(31, 45, 61, 0.65) 0%, rgba(31, 45, 61, 0.15) 100%);
  height: 12px;
}

.pace-progress-color-warning .pace-progress {
  color: #ffc107;
}

.pace-danger .pace .pace-progress {
  background: #dc3545;
}

.pace-barber-shop-danger .pace {
  background: #fff;
}

.pace-barber-shop-danger .pace .pace-progress {
  background: #dc3545;
}

.pace-barber-shop-danger .pace .pace-activity {
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);
}

.pace-big-counter-danger .pace .pace-progress::after {
  color: rgba(220, 53, 69, 0.2);
}

.pace-bounce-danger .pace .pace-activity {
  background: #dc3545;
}

.pace-center-atom-danger .pace-progress {
  height: 100px;
  width: 80px;
}

.pace-center-atom-danger .pace-progress::before {
  background: #dc3545;
  color: #fff;
  font-size: .8rem;
  line-height: .7rem;
  padding-top: 17%;
}

.pace-center-atom-danger .pace-activity {
  border-color: #dc3545;
}

.pace-center-atom-danger .pace-activity::after, .pace-center-atom-danger .pace-activity::before {
  border-color: #dc3545;
}

.pace-center-circle-danger .pace .pace-progress {
  background: rgba(220, 53, 69, 0.8);
  color: #fff;
}

.pace-center-radar-danger .pace .pace-activity {
  border-color: #dc3545 transparent transparent;
}

.pace-center-radar-danger .pace .pace-activity::before {
  border-color: #dc3545 transparent transparent;
}

.pace-center-simple-danger .pace {
  background: #fff;
  border-color: #dc3545;
}

.pace-center-simple-danger .pace .pace-progress {
  background: #dc3545;
}

.pace-material-danger .pace {
  color: #dc3545;
}

.pace-corner-indicator-danger .pace .pace-activity {
  background: #dc3545;
}

.pace-corner-indicator-danger .pace .pace-activity::after,
.pace-corner-indicator-danger .pace .pace-activity::before {
  border: 5px solid #fff;
}

.pace-corner-indicator-danger .pace .pace-activity::before {
  border-right-color: rgba(220, 53, 69, 0.2);
  border-left-color: rgba(220, 53, 69, 0.2);
}

.pace-corner-indicator-danger .pace .pace-activity::after {
  border-top-color: rgba(220, 53, 69, 0.2);
  border-bottom-color: rgba(220, 53, 69, 0.2);
}

.pace-fill-left-danger .pace .pace-progress {
  background-color: rgba(220, 53, 69, 0.2);
}

.pace-flash-danger .pace .pace-progress {
  background: #dc3545;
}

.pace-flash-danger .pace .pace-progress-inner {
  box-shadow: 0 0 10px #dc3545, 0 0 5px #dc3545;
}

.pace-flash-danger .pace .pace-activity {
  border-top-color: #dc3545;
  border-left-color: #dc3545;
}

.pace-loading-bar-danger .pace .pace-progress {
  background: #dc3545;
  color: #dc3545;
  box-shadow: 120px 0 #fff, 240px 0 #fff;
}

.pace-loading-bar-danger .pace .pace-activity {
  box-shadow: inset 0 0 0 2px #dc3545, inset 0 0 0 7px #fff;
}

.pace-mac-osx-danger .pace .pace-progress {
  background-color: #dc3545;
  box-shadow: inset -1px 0 #dc3545, inset 0 -1px #dc3545, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);
}

.pace-mac-osx-danger .pace .pace-activity {
  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);
  height: 12px;
}

.pace-progress-color-danger .pace-progress {
  color: #dc3545;
}

.pace-light .pace .pace-progress {
  background: #f8f9fa;
}

.pace-barber-shop-light .pace {
  background: #1f2d3d;
}

.pace-barber-shop-light .pace .pace-progress {
  background: #f8f9fa;
}

.pace-barber-shop-light .pace .pace-activity {
  background-image: linear-gradient(45deg, rgba(31, 45, 61, 0.2) 25%, transparent 25%, transparent 50%, rgba(31, 45, 61, 0.2) 50%, rgba(31, 45, 61, 0.2) 75%, transparent 75%, transparent);
}

.pace-big-counter-light .pace .pace-progress::after {
  color: rgba(248, 249, 250, 0.2);
}

.pace-bounce-light .pace .pace-activity {
  background: #f8f9fa;
}

.pace-center-atom-light .pace-progress {
  height: 100px;
  width: 80px;
}

.pace-center-atom-light .pace-progress::before {
  background: #f8f9fa;
  color: #1f2d3d;
  font-size: .8rem;
  line-height: .7rem;
  padding-top: 17%;
}

.pace-center-atom-light .pace-activity {
  border-color: #f8f9fa;
}

.pace-center-atom-light .pace-activity::after, .pace-center-atom-light .pace-activity::before {
  border-color: #f8f9fa;
}

.pace-center-circle-light .pace .pace-progress {
  background: rgba(248, 249, 250, 0.8);
  color: #1f2d3d;
}

.pace-center-radar-light .pace .pace-activity {
  border-color: #f8f9fa transparent transparent;
}

.pace-center-radar-light .pace .pace-activity::before {
  border-color: #f8f9fa transparent transparent;
}

.pace-center-simple-light .pace {
  background: #1f2d3d;
  border-color: #f8f9fa;
}

.pace-center-simple-light .pace .pace-progress {
  background: #f8f9fa;
}

.pace-material-light .pace {
  color: #f8f9fa;
}

.pace-corner-indicator-light .pace .pace-activity {
  background: #f8f9fa;
}

.pace-corner-indicator-light .pace .pace-activity::after,
.pace-corner-indicator-light .pace .pace-activity::before {
  border: 5px solid #1f2d3d;
}

.pace-corner-indicator-light .pace .pace-activity::before {
  border-right-color: rgba(248, 249, 250, 0.2);
  border-left-color: rgba(248, 249, 250, 0.2);
}

.pace-corner-indicator-light .pace .pace-activity::after {
  border-top-color: rgba(248, 249, 250, 0.2);
  border-bottom-color: rgba(248, 249, 250, 0.2);
}

.pace-fill-left-light .pace .pace-progress {
  background-color: rgba(248, 249, 250, 0.2);
}

.pace-flash-light .pace .pace-progress {
  background: #f8f9fa;
}

.pace-flash-light .pace .pace-progress-inner {
  box-shadow: 0 0 10px #f8f9fa, 0 0 5px #f8f9fa;
}

.pace-flash-light .pace .pace-activity {
  border-top-color: #f8f9fa;
  border-left-color: #f8f9fa;
}

.pace-loading-bar-light .pace .pace-progress {
  background: #f8f9fa;
  color: #f8f9fa;
  box-shadow: 120px 0 #1f2d3d, 240px 0 #1f2d3d;
}

.pace-loading-bar-light .pace .pace-activity {
  box-shadow: inset 0 0 0 2px #f8f9fa, inset 0 0 0 7px #1f2d3d;
}

.pace-mac-osx-light .pace .pace-progress {
  background-color: #f8f9fa;
  box-shadow: inset -1px 0 #f8f9fa, inset 0 -1px #f8f9fa, inset 0 2px rgba(31, 45, 61, 0.5), inset 0 6px rgba(31, 45, 61, 0.3);
}

.pace-mac-osx-light .pace .pace-activity {
  background-image: radial-gradient(rgba(31, 45, 61, 0.65) 0%, rgba(31, 45, 61, 0.15) 100%);
  height: 12px;
}

.pace-progress-color-light .pace-progress {
  color: #f8f9fa;
}

.pace-dark .pace .pace-progress {
  background: #343a40;
}

.pace-barber-shop-dark .pace {
  background: #fff;
}

.pace-barber-shop-dark .pace .pace-progress {
  background: #343a40;
}

.pace-barber-shop-dark .pace .pace-activity {
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);
}

.pace-big-counter-dark .pace .pace-progress::after {
  color: rgba(52, 58, 64, 0.2);
}

.pace-bounce-dark .pace .pace-activity {
  background: #343a40;
}

.pace-center-atom-dark .pace-progress {
  height: 100px;
  width: 80px;
}

.pace-center-atom-dark .pace-progress::before {
  background: #343a40;
  color: #fff;
  font-size: .8rem;
  line-height: .7rem;
  padding-top: 17%;
}

.pace-center-atom-dark .pace-activity {
  border-color: #343a40;
}

.pace-center-atom-dark .pace-activity::after, .pace-center-atom-dark .pace-activity::before {
  border-color: #343a40;
}

.pace-center-circle-dark .pace .pace-progress {
  background: rgba(52, 58, 64, 0.8);
  color: #fff;
}

.pace-center-radar-dark .pace .pace-activity {
  border-color: #343a40 transparent transparent;
}

.pace-center-radar-dark .pace .pace-activity::before {
  border-color: #343a40 transparent transparent;
}

.pace-center-simple-dark .pace {
  background: #fff;
  border-color: #343a40;
}

.pace-center-simple-dark .pace .pace-progress {
  background: #343a40;
}

.pace-material-dark .pace {
  color: #343a40;
}

.pace-corner-indicator-dark .pace .pace-activity {
  background: #343a40;
}

.pace-corner-indicator-dark .pace .pace-activity::after,
.pace-corner-indicator-dark .pace .pace-activity::before {
  border: 5px solid #fff;
}

.pace-corner-indicator-dark .pace .pace-activity::before {
  border-right-color: rgba(52, 58, 64, 0.2);
  border-left-color: rgba(52, 58, 64, 0.2);
}

.pace-corner-indicator-dark .pace .pace-activity::after {
  border-top-color: rgba(52, 58, 64, 0.2);
  border-bottom-color: rgba(52, 58, 64, 0.2);
}

.pace-fill-left-dark .pace .pace-progress {
  background-color: rgba(52, 58, 64, 0.2);
}

.pace-flash-dark .pace .pace-progress {
  background: #343a40;
}

.pace-flash-dark .pace .pace-progress-inner {
  box-shadow: 0 0 10px #343a40, 0 0 5px #343a40;
}

.pace-flash-dark .pace .pace-activity {
  border-top-color: #343a40;
  border-left-color: #343a40;
}

.pace-loading-bar-dark .pace .pace-progress {
  background: #343a40;
  color: #343a40;
  box-shadow: 120px 0 #fff, 240px 0 #fff;
}

.pace-loading-bar-dark .pace .pace-activity {
  box-shadow: inset 0 0 0 2px #343a40, inset 0 0 0 7px #fff;
}

.pace-mac-osx-dark .pace .pace-progress {
  background-color: #343a40;
  box-shadow: inset -1px 0 #343a40, inset 0 -1px #343a40, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);
}

.pace-mac-osx-dark .pace .pace-activity {
  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);
  height: 12px;
}

.pace-progress-color-dark .pace-progress {
  color: #343a40;
}

.pace-lightblue .pace .pace-progress {
  background: #3c8dbc;
}

.pace-barber-shop-lightblue .pace {
  background: #fff;
}

.pace-barber-shop-lightblue .pace .pace-progress {
  background: #3c8dbc;
}

.pace-barber-shop-lightblue .pace .pace-activity {
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);
}

.pace-big-counter-lightblue .pace .pace-progress::after {
  color: rgba(60, 141, 188, 0.2);
}

.pace-bounce-lightblue .pace .pace-activity {
  background: #3c8dbc;
}

.pace-center-atom-lightblue .pace-progress {
  height: 100px;
  width: 80px;
}

.pace-center-atom-lightblue .pace-progress::before {
  background: #3c8dbc;
  color: #fff;
  font-size: .8rem;
  line-height: .7rem;
  padding-top: 17%;
}

.pace-center-atom-lightblue .pace-activity {
  border-color: #3c8dbc;
}

.pace-center-atom-lightblue .pace-activity::after, .pace-center-atom-lightblue .pace-activity::before {
  border-color: #3c8dbc;
}

.pace-center-circle-lightblue .pace .pace-progress {
  background: rgba(60, 141, 188, 0.8);
  color: #fff;
}

.pace-center-radar-lightblue .pace .pace-activity {
  border-color: #3c8dbc transparent transparent;
}

.pace-center-radar-lightblue .pace .pace-activity::before {
  border-color: #3c8dbc transparent transparent;
}

.pace-center-simple-lightblue .pace {
  background: #fff;
  border-color: #3c8dbc;
}

.pace-center-simple-lightblue .pace .pace-progress {
  background: #3c8dbc;
}

.pace-material-lightblue .pace {
  color: #3c8dbc;
}

.pace-corner-indicator-lightblue .pace .pace-activity {
  background: #3c8dbc;
}

.pace-corner-indicator-lightblue .pace .pace-activity::after,
.pace-corner-indicator-lightblue .pace .pace-activity::before {
  border: 5px solid #fff;
}

.pace-corner-indicator-lightblue .pace .pace-activity::before {
  border-right-color: rgba(60, 141, 188, 0.2);
  border-left-color: rgba(60, 141, 188, 0.2);
}

.pace-corner-indicator-lightblue .pace .pace-activity::after {
  border-top-color: rgba(60, 141, 188, 0.2);
  border-bottom-color: rgba(60, 141, 188, 0.2);
}

.pace-fill-left-lightblue .pace .pace-progress {
  background-color: rgba(60, 141, 188, 0.2);
}

.pace-flash-lightblue .pace .pace-progress {
  background: #3c8dbc;
}

.pace-flash-lightblue .pace .pace-progress-inner {
  box-shadow: 0 0 10px #3c8dbc, 0 0 5px #3c8dbc;
}

.pace-flash-lightblue .pace .pace-activity {
  border-top-color: #3c8dbc;
  border-left-color: #3c8dbc;
}

.pace-loading-bar-lightblue .pace .pace-progress {
  background: #3c8dbc;
  color: #3c8dbc;
  box-shadow: 120px 0 #fff, 240px 0 #fff;
}

.pace-loading-bar-lightblue .pace .pace-activity {
  box-shadow: inset 0 0 0 2px #3c8dbc, inset 0 0 0 7px #fff;
}

.pace-mac-osx-lightblue .pace .pace-progress {
  background-color: #3c8dbc;
  box-shadow: inset -1px 0 #3c8dbc, inset 0 -1px #3c8dbc, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);
}

.pace-mac-osx-lightblue .pace .pace-activity {
  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);
  height: 12px;
}

.pace-progress-color-lightblue .pace-progress {
  color: #3c8dbc;
}

.pace-navy .pace .pace-progress {
  background: #001f3f;
}

.pace-barber-shop-navy .pace {
  background: #fff;
}

.pace-barber-shop-navy .pace .pace-progress {
  background: #001f3f;
}

.pace-barber-shop-navy .pace .pace-activity {
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);
}

.pace-big-counter-navy .pace .pace-progress::after {
  color: rgba(0, 31, 63, 0.2);
}

.pace-bounce-navy .pace .pace-activity {
  background: #001f3f;
}

.pace-center-atom-navy .pace-progress {
  height: 100px;
  width: 80px;
}

.pace-center-atom-navy .pace-progress::before {
  background: #001f3f;
  color: #fff;
  font-size: .8rem;
  line-height: .7rem;
  padding-top: 17%;
}

.pace-center-atom-navy .pace-activity {
  border-color: #001f3f;
}

.pace-center-atom-navy .pace-activity::after, .pace-center-atom-navy .pace-activity::before {
  border-color: #001f3f;
}

.pace-center-circle-navy .pace .pace-progress {
  background: rgba(0, 31, 63, 0.8);
  color: #fff;
}

.pace-center-radar-navy .pace .pace-activity {
  border-color: #001f3f transparent transparent;
}

.pace-center-radar-navy .pace .pace-activity::before {
  border-color: #001f3f transparent transparent;
}

.pace-center-simple-navy .pace {
  background: #fff;
  border-color: #001f3f;
}

.pace-center-simple-navy .pace .pace-progress {
  background: #001f3f;
}

.pace-material-navy .pace {
  color: #001f3f;
}

.pace-corner-indicator-navy .pace .pace-activity {
  background: #001f3f;
}

.pace-corner-indicator-navy .pace .pace-activity::after,
.pace-corner-indicator-navy .pace .pace-activity::before {
  border: 5px solid #fff;
}

.pace-corner-indicator-navy .pace .pace-activity::before {
  border-right-color: rgba(0, 31, 63, 0.2);
  border-left-color: rgba(0, 31, 63, 0.2);
}

.pace-corner-indicator-navy .pace .pace-activity::after {
  border-top-color: rgba(0, 31, 63, 0.2);
  border-bottom-color: rgba(0, 31, 63, 0.2);
}

.pace-fill-left-navy .pace .pace-progress {
  background-color: rgba(0, 31, 63, 0.2);
}

.pace-flash-navy .pace .pace-progress {
  background: #001f3f;
}

.pace-flash-navy .pace .pace-progress-inner {
  box-shadow: 0 0 10px #001f3f, 0 0 5px #001f3f;
}

.pace-flash-navy .pace .pace-activity {
  border-top-color: #001f3f;
  border-left-color: #001f3f;
}

.pace-loading-bar-navy .pace .pace-progress {
  background: #001f3f;
  color: #001f3f;
  box-shadow: 120px 0 #fff, 240px 0 #fff;
}

.pace-loading-bar-navy .pace .pace-activity {
  box-shadow: inset 0 0 0 2px #001f3f, inset 0 0 0 7px #fff;
}

.pace-mac-osx-navy .pace .pace-progress {
  background-color: #001f3f;
  box-shadow: inset -1px 0 #001f3f, inset 0 -1px #001f3f, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);
}

.pace-mac-osx-navy .pace .pace-activity {
  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);
  height: 12px;
}

.pace-progress-color-navy .pace-progress {
  color: #001f3f;
}

.pace-olive .pace .pace-progress {
  background: #3d9970;
}

.pace-barber-shop-olive .pace {
  background: #fff;
}

.pace-barber-shop-olive .pace .pace-progress {
  background: #3d9970;
}

.pace-barber-shop-olive .pace .pace-activity {
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);
}

.pace-big-counter-olive .pace .pace-progress::after {
  color: rgba(61, 153, 112, 0.2);
}

.pace-bounce-olive .pace .pace-activity {
  background: #3d9970;
}

.pace-center-atom-olive .pace-progress {
  height: 100px;
  width: 80px;
}

.pace-center-atom-olive .pace-progress::before {
  background: #3d9970;
  color: #fff;
  font-size: .8rem;
  line-height: .7rem;
  padding-top: 17%;
}

.pace-center-atom-olive .pace-activity {
  border-color: #3d9970;
}

.pace-center-atom-olive .pace-activity::after, .pace-center-atom-olive .pace-activity::before {
  border-color: #3d9970;
}

.pace-center-circle-olive .pace .pace-progress {
  background: rgba(61, 153, 112, 0.8);
  color: #fff;
}

.pace-center-radar-olive .pace .pace-activity {
  border-color: #3d9970 transparent transparent;
}

.pace-center-radar-olive .pace .pace-activity::before {
  border-color: #3d9970 transparent transparent;
}

.pace-center-simple-olive .pace {
  background: #fff;
  border-color: #3d9970;
}

.pace-center-simple-olive .pace .pace-progress {
  background: #3d9970;
}

.pace-material-olive .pace {
  color: #3d9970;
}

.pace-corner-indicator-olive .pace .pace-activity {
  background: #3d9970;
}

.pace-corner-indicator-olive .pace .pace-activity::after,
.pace-corner-indicator-olive .pace .pace-activity::before {
  border: 5px solid #fff;
}

.pace-corner-indicator-olive .pace .pace-activity::before {
  border-right-color: rgba(61, 153, 112, 0.2);
  border-left-color: rgba(61, 153, 112, 0.2);
}

.pace-corner-indicator-olive .pace .pace-activity::after {
  border-top-color: rgba(61, 153, 112, 0.2);
  border-bottom-color: rgba(61, 153, 112, 0.2);
}

.pace-fill-left-olive .pace .pace-progress {
  background-color: rgba(61, 153, 112, 0.2);
}

.pace-flash-olive .pace .pace-progress {
  background: #3d9970;
}

.pace-flash-olive .pace .pace-progress-inner {
  box-shadow: 0 0 10px #3d9970, 0 0 5px #3d9970;
}

.pace-flash-olive .pace .pace-activity {
  border-top-color: #3d9970;
  border-left-color: #3d9970;
}

.pace-loading-bar-olive .pace .pace-progress {
  background: #3d9970;
  color: #3d9970;
  box-shadow: 120px 0 #fff, 240px 0 #fff;
}

.pace-loading-bar-olive .pace .pace-activity {
  box-shadow: inset 0 0 0 2px #3d9970, inset 0 0 0 7px #fff;
}

.pace-mac-osx-olive .pace .pace-progress {
  background-color: #3d9970;
  box-shadow: inset -1px 0 #3d9970, inset 0 -1px #3d9970, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);
}

.pace-mac-osx-olive .pace .pace-activity {
  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);
  height: 12px;
}

.pace-progress-color-olive .pace-progress {
  color: #3d9970;
}

.pace-lime .pace .pace-progress {
  background: #01ff70;
}

.pace-barber-shop-lime .pace {
  background: #1f2d3d;
}

.pace-barber-shop-lime .pace .pace-progress {
  background: #01ff70;
}

.pace-barber-shop-lime .pace .pace-activity {
  background-image: linear-gradient(45deg, rgba(31, 45, 61, 0.2) 25%, transparent 25%, transparent 50%, rgba(31, 45, 61, 0.2) 50%, rgba(31, 45, 61, 0.2) 75%, transparent 75%, transparent);
}

.pace-big-counter-lime .pace .pace-progress::after {
  color: rgba(1, 255, 112, 0.2);
}

.pace-bounce-lime .pace .pace-activity {
  background: #01ff70;
}

.pace-center-atom-lime .pace-progress {
  height: 100px;
  width: 80px;
}

.pace-center-atom-lime .pace-progress::before {
  background: #01ff70;
  color: #1f2d3d;
  font-size: .8rem;
  line-height: .7rem;
  padding-top: 17%;
}

.pace-center-atom-lime .pace-activity {
  border-color: #01ff70;
}

.pace-center-atom-lime .pace-activity::after, .pace-center-atom-lime .pace-activity::before {
  border-color: #01ff70;
}

.pace-center-circle-lime .pace .pace-progress {
  background: rgba(1, 255, 112, 0.8);
  color: #1f2d3d;
}

.pace-center-radar-lime .pace .pace-activity {
  border-color: #01ff70 transparent transparent;
}

.pace-center-radar-lime .pace .pace-activity::before {
  border-color: #01ff70 transparent transparent;
}

.pace-center-simple-lime .pace {
  background: #1f2d3d;
  border-color: #01ff70;
}

.pace-center-simple-lime .pace .pace-progress {
  background: #01ff70;
}

.pace-material-lime .pace {
  color: #01ff70;
}

.pace-corner-indicator-lime .pace .pace-activity {
  background: #01ff70;
}

.pace-corner-indicator-lime .pace .pace-activity::after,
.pace-corner-indicator-lime .pace .pace-activity::before {
  border: 5px solid #1f2d3d;
}

.pace-corner-indicator-lime .pace .pace-activity::before {
  border-right-color: rgba(1, 255, 112, 0.2);
  border-left-color: rgba(1, 255, 112, 0.2);
}

.pace-corner-indicator-lime .pace .pace-activity::after {
  border-top-color: rgba(1, 255, 112, 0.2);
  border-bottom-color: rgba(1, 255, 112, 0.2);
}

.pace-fill-left-lime .pace .pace-progress {
  background-color: rgba(1, 255, 112, 0.2);
}

.pace-flash-lime .pace .pace-progress {
  background: #01ff70;
}

.pace-flash-lime .pace .pace-progress-inner {
  box-shadow: 0 0 10px #01ff70, 0 0 5px #01ff70;
}

.pace-flash-lime .pace .pace-activity {
  border-top-color: #01ff70;
  border-left-color: #01ff70;
}

.pace-loading-bar-lime .pace .pace-progress {
  background: #01ff70;
  color: #01ff70;
  box-shadow: 120px 0 #1f2d3d, 240px 0 #1f2d3d;
}

.pace-loading-bar-lime .pace .pace-activity {
  box-shadow: inset 0 0 0 2px #01ff70, inset 0 0 0 7px #1f2d3d;
}

.pace-mac-osx-lime .pace .pace-progress {
  background-color: #01ff70;
  box-shadow: inset -1px 0 #01ff70, inset 0 -1px #01ff70, inset 0 2px rgba(31, 45, 61, 0.5), inset 0 6px rgba(31, 45, 61, 0.3);
}

.pace-mac-osx-lime .pace .pace-activity {
  background-image: radial-gradient(rgba(31, 45, 61, 0.65) 0%, rgba(31, 45, 61, 0.15) 100%);
  height: 12px;
}

.pace-progress-color-lime .pace-progress {
  color: #01ff70;
}

.pace-fuchsia .pace .pace-progress {
  background: #f012be;
}

.pace-barber-shop-fuchsia .pace {
  background: #fff;
}

.pace-barber-shop-fuchsia .pace .pace-progress {
  background: #f012be;
}

.pace-barber-shop-fuchsia .pace .pace-activity {
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);
}

.pace-big-counter-fuchsia .pace .pace-progress::after {
  color: rgba(240, 18, 190, 0.2);
}

.pace-bounce-fuchsia .pace .pace-activity {
  background: #f012be;
}

.pace-center-atom-fuchsia .pace-progress {
  height: 100px;
  width: 80px;
}

.pace-center-atom-fuchsia .pace-progress::before {
  background: #f012be;
  color: #fff;
  font-size: .8rem;
  line-height: .7rem;
  padding-top: 17%;
}

.pace-center-atom-fuchsia .pace-activity {
  border-color: #f012be;
}

.pace-center-atom-fuchsia .pace-activity::after, .pace-center-atom-fuchsia .pace-activity::before {
  border-color: #f012be;
}

.pace-center-circle-fuchsia .pace .pace-progress {
  background: rgba(240, 18, 190, 0.8);
  color: #fff;
}

.pace-center-radar-fuchsia .pace .pace-activity {
  border-color: #f012be transparent transparent;
}

.pace-center-radar-fuchsia .pace .pace-activity::before {
  border-color: #f012be transparent transparent;
}

.pace-center-simple-fuchsia .pace {
  background: #fff;
  border-color: #f012be;
}

.pace-center-simple-fuchsia .pace .pace-progress {
  background: #f012be;
}

.pace-material-fuchsia .pace {
  color: #f012be;
}

.pace-corner-indicator-fuchsia .pace .pace-activity {
  background: #f012be;
}

.pace-corner-indicator-fuchsia .pace .pace-activity::after,
.pace-corner-indicator-fuchsia .pace .pace-activity::before {
  border: 5px solid #fff;
}

.pace-corner-indicator-fuchsia .pace .pace-activity::before {
  border-right-color: rgba(240, 18, 190, 0.2);
  border-left-color: rgba(240, 18, 190, 0.2);
}

.pace-corner-indicator-fuchsia .pace .pace-activity::after {
  border-top-color: rgba(240, 18, 190, 0.2);
  border-bottom-color: rgba(240, 18, 190, 0.2);
}

.pace-fill-left-fuchsia .pace .pace-progress {
  background-color: rgba(240, 18, 190, 0.2);
}

.pace-flash-fuchsia .pace .pace-progress {
  background: #f012be;
}

.pace-flash-fuchsia .pace .pace-progress-inner {
  box-shadow: 0 0 10px #f012be, 0 0 5px #f012be;
}

.pace-flash-fuchsia .pace .pace-activity {
  border-top-color: #f012be;
  border-left-color: #f012be;
}

.pace-loading-bar-fuchsia .pace .pace-progress {
  background: #f012be;
  color: #f012be;
  box-shadow: 120px 0 #fff, 240px 0 #fff;
}

.pace-loading-bar-fuchsia .pace .pace-activity {
  box-shadow: inset 0 0 0 2px #f012be, inset 0 0 0 7px #fff;
}

.pace-mac-osx-fuchsia .pace .pace-progress {
  background-color: #f012be;
  box-shadow: inset -1px 0 #f012be, inset 0 -1px #f012be, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);
}

.pace-mac-osx-fuchsia .pace .pace-activity {
  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);
  height: 12px;
}

.pace-progress-color-fuchsia .pace-progress {
  color: #f012be;
}

.pace-maroon .pace .pace-progress {
  background: #d81b60;
}

.pace-barber-shop-maroon .pace {
  background: #fff;
}

.pace-barber-shop-maroon .pace .pace-progress {
  background: #d81b60;
}

.pace-barber-shop-maroon .pace .pace-activity {
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);
}

.pace-big-counter-maroon .pace .pace-progress::after {
  color: rgba(216, 27, 96, 0.2);
}

.pace-bounce-maroon .pace .pace-activity {
  background: #d81b60;
}

.pace-center-atom-maroon .pace-progress {
  height: 100px;
  width: 80px;
}

.pace-center-atom-maroon .pace-progress::before {
  background: #d81b60;
  color: #fff;
  font-size: .8rem;
  line-height: .7rem;
  padding-top: 17%;
}

.pace-center-atom-maroon .pace-activity {
  border-color: #d81b60;
}

.pace-center-atom-maroon .pace-activity::after, .pace-center-atom-maroon .pace-activity::before {
  border-color: #d81b60;
}

.pace-center-circle-maroon .pace .pace-progress {
  background: rgba(216, 27, 96, 0.8);
  color: #fff;
}

.pace-center-radar-maroon .pace .pace-activity {
  border-color: #d81b60 transparent transparent;
}

.pace-center-radar-maroon .pace .pace-activity::before {
  border-color: #d81b60 transparent transparent;
}

.pace-center-simple-maroon .pace {
  background: #fff;
  border-color: #d81b60;
}

.pace-center-simple-maroon .pace .pace-progress {
  background: #d81b60;
}

.pace-material-maroon .pace {
  color: #d81b60;
}

.pace-corner-indicator-maroon .pace .pace-activity {
  background: #d81b60;
}

.pace-corner-indicator-maroon .pace .pace-activity::after,
.pace-corner-indicator-maroon .pace .pace-activity::before {
  border: 5px solid #fff;
}

.pace-corner-indicator-maroon .pace .pace-activity::before {
  border-right-color: rgba(216, 27, 96, 0.2);
  border-left-color: rgba(216, 27, 96, 0.2);
}

.pace-corner-indicator-maroon .pace .pace-activity::after {
  border-top-color: rgba(216, 27, 96, 0.2);
  border-bottom-color: rgba(216, 27, 96, 0.2);
}

.pace-fill-left-maroon .pace .pace-progress {
  background-color: rgba(216, 27, 96, 0.2);
}

.pace-flash-maroon .pace .pace-progress {
  background: #d81b60;
}

.pace-flash-maroon .pace .pace-progress-inner {
  box-shadow: 0 0 10px #d81b60, 0 0 5px #d81b60;
}

.pace-flash-maroon .pace .pace-activity {
  border-top-color: #d81b60;
  border-left-color: #d81b60;
}

.pace-loading-bar-maroon .pace .pace-progress {
  background: #d81b60;
  color: #d81b60;
  box-shadow: 120px 0 #fff, 240px 0 #fff;
}

.pace-loading-bar-maroon .pace .pace-activity {
  box-shadow: inset 0 0 0 2px #d81b60, inset 0 0 0 7px #fff;
}

.pace-mac-osx-maroon .pace .pace-progress {
  background-color: #d81b60;
  box-shadow: inset -1px 0 #d81b60, inset 0 -1px #d81b60, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);
}

.pace-mac-osx-maroon .pace .pace-activity {
  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);
  height: 12px;
}

.pace-progress-color-maroon .pace-progress {
  color: #d81b60;
}

.pace-blue .pace .pace-progress {
  background: #007bff;
}

.pace-barber-shop-blue .pace {
  background: #fff;
}

.pace-barber-shop-blue .pace .pace-progress {
  background: #007bff;
}

.pace-barber-shop-blue .pace .pace-activity {
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);
}

.pace-big-counter-blue .pace .pace-progress::after {
  color: rgba(0, 123, 255, 0.2);
}

.pace-bounce-blue .pace .pace-activity {
  background: #007bff;
}

.pace-center-atom-blue .pace-progress {
  height: 100px;
  width: 80px;
}

.pace-center-atom-blue .pace-progress::before {
  background: #007bff;
  color: #fff;
  font-size: .8rem;
  line-height: .7rem;
  padding-top: 17%;
}

.pace-center-atom-blue .pace-activity {
  border-color: #007bff;
}

.pace-center-atom-blue .pace-activity::after, .pace-center-atom-blue .pace-activity::before {
  border-color: #007bff;
}

.pace-center-circle-blue .pace .pace-progress {
  background: rgba(0, 123, 255, 0.8);
  color: #fff;
}

.pace-center-radar-blue .pace .pace-activity {
  border-color: #007bff transparent transparent;
}

.pace-center-radar-blue .pace .pace-activity::before {
  border-color: #007bff transparent transparent;
}

.pace-center-simple-blue .pace {
  background: #fff;
  border-color: #007bff;
}

.pace-center-simple-blue .pace .pace-progress {
  background: #007bff;
}

.pace-material-blue .pace {
  color: #007bff;
}

.pace-corner-indicator-blue .pace .pace-activity {
  background: #007bff;
}

.pace-corner-indicator-blue .pace .pace-activity::after,
.pace-corner-indicator-blue .pace .pace-activity::before {
  border: 5px solid #fff;
}

.pace-corner-indicator-blue .pace .pace-activity::before {
  border-right-color: rgba(0, 123, 255, 0.2);
  border-left-color: rgba(0, 123, 255, 0.2);
}

.pace-corner-indicator-blue .pace .pace-activity::after {
  border-top-color: rgba(0, 123, 255, 0.2);
  border-bottom-color: rgba(0, 123, 255, 0.2);
}

.pace-fill-left-blue .pace .pace-progress {
  background-color: rgba(0, 123, 255, 0.2);
}

.pace-flash-blue .pace .pace-progress {
  background: #007bff;
}

.pace-flash-blue .pace .pace-progress-inner {
  box-shadow: 0 0 10px #007bff, 0 0 5px #007bff;
}

.pace-flash-blue .pace .pace-activity {
  border-top-color: #007bff;
  border-left-color: #007bff;
}

.pace-loading-bar-blue .pace .pace-progress {
  background: #007bff;
  color: #007bff;
  box-shadow: 120px 0 #fff, 240px 0 #fff;
}

.pace-loading-bar-blue .pace .pace-activity {
  box-shadow: inset 0 0 0 2px #007bff, inset 0 0 0 7px #fff;
}

.pace-mac-osx-blue .pace .pace-progress {
  background-color: #007bff;
  box-shadow: inset -1px 0 #007bff, inset 0 -1px #007bff, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);
}

.pace-mac-osx-blue .pace .pace-activity {
  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);
  height: 12px;
}

.pace-progress-color-blue .pace-progress {
  color: #007bff;
}

.pace-indigo .pace .pace-progress {
  background: #6610f2;
}

.pace-barber-shop-indigo .pace {
  background: #fff;
}

.pace-barber-shop-indigo .pace .pace-progress {
  background: #6610f2;
}

.pace-barber-shop-indigo .pace .pace-activity {
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);
}

.pace-big-counter-indigo .pace .pace-progress::after {
  color: rgba(102, 16, 242, 0.2);
}

.pace-bounce-indigo .pace .pace-activity {
  background: #6610f2;
}

.pace-center-atom-indigo .pace-progress {
  height: 100px;
  width: 80px;
}

.pace-center-atom-indigo .pace-progress::before {
  background: #6610f2;
  color: #fff;
  font-size: .8rem;
  line-height: .7rem;
  padding-top: 17%;
}

.pace-center-atom-indigo .pace-activity {
  border-color: #6610f2;
}

.pace-center-atom-indigo .pace-activity::after, .pace-center-atom-indigo .pace-activity::before {
  border-color: #6610f2;
}

.pace-center-circle-indigo .pace .pace-progress {
  background: rgba(102, 16, 242, 0.8);
  color: #fff;
}

.pace-center-radar-indigo .pace .pace-activity {
  border-color: #6610f2 transparent transparent;
}

.pace-center-radar-indigo .pace .pace-activity::before {
  border-color: #6610f2 transparent transparent;
}

.pace-center-simple-indigo .pace {
  background: #fff;
  border-color: #6610f2;
}

.pace-center-simple-indigo .pace .pace-progress {
  background: #6610f2;
}

.pace-material-indigo .pace {
  color: #6610f2;
}

.pace-corner-indicator-indigo .pace .pace-activity {
  background: #6610f2;
}

.pace-corner-indicator-indigo .pace .pace-activity::after,
.pace-corner-indicator-indigo .pace .pace-activity::before {
  border: 5px solid #fff;
}

.pace-corner-indicator-indigo .pace .pace-activity::before {
  border-right-color: rgba(102, 16, 242, 0.2);
  border-left-color: rgba(102, 16, 242, 0.2);
}

.pace-corner-indicator-indigo .pace .pace-activity::after {
  border-top-color: rgba(102, 16, 242, 0.2);
  border-bottom-color: rgba(102, 16, 242, 0.2);
}

.pace-fill-left-indigo .pace .pace-progress {
  background-color: rgba(102, 16, 242, 0.2);
}

.pace-flash-indigo .pace .pace-progress {
  background: #6610f2;
}

.pace-flash-indigo .pace .pace-progress-inner {
  box-shadow: 0 0 10px #6610f2, 0 0 5px #6610f2;
}

.pace-flash-indigo .pace .pace-activity {
  border-top-color: #6610f2;
  border-left-color: #6610f2;
}

.pace-loading-bar-indigo .pace .pace-progress {
  background: #6610f2;
  color: #6610f2;
  box-shadow: 120px 0 #fff, 240px 0 #fff;
}

.pace-loading-bar-indigo .pace .pace-activity {
  box-shadow: inset 0 0 0 2px #6610f2, inset 0 0 0 7px #fff;
}

.pace-mac-osx-indigo .pace .pace-progress {
  background-color: #6610f2;
  box-shadow: inset -1px 0 #6610f2, inset 0 -1px #6610f2, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);
}

.pace-mac-osx-indigo .pace .pace-activity {
  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);
  height: 12px;
}

.pace-progress-color-indigo .pace-progress {
  color: #6610f2;
}

.pace-purple .pace .pace-progress {
  background: #6f42c1;
}

.pace-barber-shop-purple .pace {
  background: #fff;
}

.pace-barber-shop-purple .pace .pace-progress {
  background: #6f42c1;
}

.pace-barber-shop-purple .pace .pace-activity {
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);
}

.pace-big-counter-purple .pace .pace-progress::after {
  color: rgba(111, 66, 193, 0.2);
}

.pace-bounce-purple .pace .pace-activity {
  background: #6f42c1;
}

.pace-center-atom-purple .pace-progress {
  height: 100px;
  width: 80px;
}

.pace-center-atom-purple .pace-progress::before {
  background: #6f42c1;
  color: #fff;
  font-size: .8rem;
  line-height: .7rem;
  padding-top: 17%;
}

.pace-center-atom-purple .pace-activity {
  border-color: #6f42c1;
}

.pace-center-atom-purple .pace-activity::after, .pace-center-atom-purple .pace-activity::before {
  border-color: #6f42c1;
}

.pace-center-circle-purple .pace .pace-progress {
  background: rgba(111, 66, 193, 0.8);
  color: #fff;
}

.pace-center-radar-purple .pace .pace-activity {
  border-color: #6f42c1 transparent transparent;
}

.pace-center-radar-purple .pace .pace-activity::before {
  border-color: #6f42c1 transparent transparent;
}

.pace-center-simple-purple .pace {
  background: #fff;
  border-color: #6f42c1;
}

.pace-center-simple-purple .pace .pace-progress {
  background: #6f42c1;
}

.pace-material-purple .pace {
  color: #6f42c1;
}

.pace-corner-indicator-purple .pace .pace-activity {
  background: #6f42c1;
}

.pace-corner-indicator-purple .pace .pace-activity::after,
.pace-corner-indicator-purple .pace .pace-activity::before {
  border: 5px solid #fff;
}

.pace-corner-indicator-purple .pace .pace-activity::before {
  border-right-color: rgba(111, 66, 193, 0.2);
  border-left-color: rgba(111, 66, 193, 0.2);
}

.pace-corner-indicator-purple .pace .pace-activity::after {
  border-top-color: rgba(111, 66, 193, 0.2);
  border-bottom-color: rgba(111, 66, 193, 0.2);
}

.pace-fill-left-purple .pace .pace-progress {
  background-color: rgba(111, 66, 193, 0.2);
}

.pace-flash-purple .pace .pace-progress {
  background: #6f42c1;
}

.pace-flash-purple .pace .pace-progress-inner {
  box-shadow: 0 0 10px #6f42c1, 0 0 5px #6f42c1;
}

.pace-flash-purple .pace .pace-activity {
  border-top-color: #6f42c1;
  border-left-color: #6f42c1;
}

.pace-loading-bar-purple .pace .pace-progress {
  background: #6f42c1;
  color: #6f42c1;
  box-shadow: 120px 0 #fff, 240px 0 #fff;
}

.pace-loading-bar-purple .pace .pace-activity {
  box-shadow: inset 0 0 0 2px #6f42c1, inset 0 0 0 7px #fff;
}

.pace-mac-osx-purple .pace .pace-progress {
  background-color: #6f42c1;
  box-shadow: inset -1px 0 #6f42c1, inset 0 -1px #6f42c1, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);
}

.pace-mac-osx-purple .pace .pace-activity {
  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);
  height: 12px;
}

.pace-progress-color-purple .pace-progress {
  color: #6f42c1;
}

.pace-pink .pace .pace-progress {
  background: #e83e8c;
}

.pace-barber-shop-pink .pace {
  background: #fff;
}

.pace-barber-shop-pink .pace .pace-progress {
  background: #e83e8c;
}

.pace-barber-shop-pink .pace .pace-activity {
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);
}

.pace-big-counter-pink .pace .pace-progress::after {
  color: rgba(232, 62, 140, 0.2);
}

.pace-bounce-pink .pace .pace-activity {
  background: #e83e8c;
}

.pace-center-atom-pink .pace-progress {
  height: 100px;
  width: 80px;
}

.pace-center-atom-pink .pace-progress::before {
  background: #e83e8c;
  color: #fff;
  font-size: .8rem;
  line-height: .7rem;
  padding-top: 17%;
}

.pace-center-atom-pink .pace-activity {
  border-color: #e83e8c;
}

.pace-center-atom-pink .pace-activity::after, .pace-center-atom-pink .pace-activity::before {
  border-color: #e83e8c;
}

.pace-center-circle-pink .pace .pace-progress {
  background: rgba(232, 62, 140, 0.8);
  color: #fff;
}

.pace-center-radar-pink .pace .pace-activity {
  border-color: #e83e8c transparent transparent;
}

.pace-center-radar-pink .pace .pace-activity::before {
  border-color: #e83e8c transparent transparent;
}

.pace-center-simple-pink .pace {
  background: #fff;
  border-color: #e83e8c;
}

.pace-center-simple-pink .pace .pace-progress {
  background: #e83e8c;
}

.pace-material-pink .pace {
  color: #e83e8c;
}

.pace-corner-indicator-pink .pace .pace-activity {
  background: #e83e8c;
}

.pace-corner-indicator-pink .pace .pace-activity::after,
.pace-corner-indicator-pink .pace .pace-activity::before {
  border: 5px solid #fff;
}

.pace-corner-indicator-pink .pace .pace-activity::before {
  border-right-color: rgba(232, 62, 140, 0.2);
  border-left-color: rgba(232, 62, 140, 0.2);
}

.pace-corner-indicator-pink .pace .pace-activity::after {
  border-top-color: rgba(232, 62, 140, 0.2);
  border-bottom-color: rgba(232, 62, 140, 0.2);
}

.pace-fill-left-pink .pace .pace-progress {
  background-color: rgba(232, 62, 140, 0.2);
}

.pace-flash-pink .pace .pace-progress {
  background: #e83e8c;
}

.pace-flash-pink .pace .pace-progress-inner {
  box-shadow: 0 0 10px #e83e8c, 0 0 5px #e83e8c;
}

.pace-flash-pink .pace .pace-activity {
  border-top-color: #e83e8c;
  border-left-color: #e83e8c;
}

.pace-loading-bar-pink .pace .pace-progress {
  background: #e83e8c;
  color: #e83e8c;
  box-shadow: 120px 0 #fff, 240px 0 #fff;
}

.pace-loading-bar-pink .pace .pace-activity {
  box-shadow: inset 0 0 0 2px #e83e8c, inset 0 0 0 7px #fff;
}

.pace-mac-osx-pink .pace .pace-progress {
  background-color: #e83e8c;
  box-shadow: inset -1px 0 #e83e8c, inset 0 -1px #e83e8c, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);
}

.pace-mac-osx-pink .pace .pace-activity {
  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);
  height: 12px;
}

.pace-progress-color-pink .pace-progress {
  color: #e83e8c;
}

.pace-red .pace .pace-progress {
  background: #dc3545;
}

.pace-barber-shop-red .pace {
  background: #fff;
}

.pace-barber-shop-red .pace .pace-progress {
  background: #dc3545;
}

.pace-barber-shop-red .pace .pace-activity {
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);
}

.pace-big-counter-red .pace .pace-progress::after {
  color: rgba(220, 53, 69, 0.2);
}

.pace-bounce-red .pace .pace-activity {
  background: #dc3545;
}

.pace-center-atom-red .pace-progress {
  height: 100px;
  width: 80px;
}

.pace-center-atom-red .pace-progress::before {
  background: #dc3545;
  color: #fff;
  font-size: .8rem;
  line-height: .7rem;
  padding-top: 17%;
}

.pace-center-atom-red .pace-activity {
  border-color: #dc3545;
}

.pace-center-atom-red .pace-activity::after, .pace-center-atom-red .pace-activity::before {
  border-color: #dc3545;
}

.pace-center-circle-red .pace .pace-progress {
  background: rgba(220, 53, 69, 0.8);
  color: #fff;
}

.pace-center-radar-red .pace .pace-activity {
  border-color: #dc3545 transparent transparent;
}

.pace-center-radar-red .pace .pace-activity::before {
  border-color: #dc3545 transparent transparent;
}

.pace-center-simple-red .pace {
  background: #fff;
  border-color: #dc3545;
}

.pace-center-simple-red .pace .pace-progress {
  background: #dc3545;
}

.pace-material-red .pace {
  color: #dc3545;
}

.pace-corner-indicator-red .pace .pace-activity {
  background: #dc3545;
}

.pace-corner-indicator-red .pace .pace-activity::after,
.pace-corner-indicator-red .pace .pace-activity::before {
  border: 5px solid #fff;
}

.pace-corner-indicator-red .pace .pace-activity::before {
  border-right-color: rgba(220, 53, 69, 0.2);
  border-left-color: rgba(220, 53, 69, 0.2);
}

.pace-corner-indicator-red .pace .pace-activity::after {
  border-top-color: rgba(220, 53, 69, 0.2);
  border-bottom-color: rgba(220, 53, 69, 0.2);
}

.pace-fill-left-red .pace .pace-progress {
  background-color: rgba(220, 53, 69, 0.2);
}

.pace-flash-red .pace .pace-progress {
  background: #dc3545;
}

.pace-flash-red .pace .pace-progress-inner {
  box-shadow: 0 0 10px #dc3545, 0 0 5px #dc3545;
}

.pace-flash-red .pace .pace-activity {
  border-top-color: #dc3545;
  border-left-color: #dc3545;
}

.pace-loading-bar-red .pace .pace-progress {
  background: #dc3545;
  color: #dc3545;
  box-shadow: 120px 0 #fff, 240px 0 #fff;
}

.pace-loading-bar-red .pace .pace-activity {
  box-shadow: inset 0 0 0 2px #dc3545, inset 0 0 0 7px #fff;
}

.pace-mac-osx-red .pace .pace-progress {
  background-color: #dc3545;
  box-shadow: inset -1px 0 #dc3545, inset 0 -1px #dc3545, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);
}

.pace-mac-osx-red .pace .pace-activity {
  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);
  height: 12px;
}

.pace-progress-color-red .pace-progress {
  color: #dc3545;
}

.pace-orange .pace .pace-progress {
  background: #fd7e14;
}

.pace-barber-shop-orange .pace {
  background: #1f2d3d;
}

.pace-barber-shop-orange .pace .pace-progress {
  background: #fd7e14;
}

.pace-barber-shop-orange .pace .pace-activity {
  background-image: linear-gradient(45deg, rgba(31, 45, 61, 0.2) 25%, transparent 25%, transparent 50%, rgba(31, 45, 61, 0.2) 50%, rgba(31, 45, 61, 0.2) 75%, transparent 75%, transparent);
}

.pace-big-counter-orange .pace .pace-progress::after {
  color: rgba(253, 126, 20, 0.2);
}

.pace-bounce-orange .pace .pace-activity {
  background: #fd7e14;
}

.pace-center-atom-orange .pace-progress {
  height: 100px;
  width: 80px;
}

.pace-center-atom-orange .pace-progress::before {
  background: #fd7e14;
  color: #1f2d3d;
  font-size: .8rem;
  line-height: .7rem;
  padding-top: 17%;
}

.pace-center-atom-orange .pace-activity {
  border-color: #fd7e14;
}

.pace-center-atom-orange .pace-activity::after, .pace-center-atom-orange .pace-activity::before {
  border-color: #fd7e14;
}

.pace-center-circle-orange .pace .pace-progress {
  background: rgba(253, 126, 20, 0.8);
  color: #1f2d3d;
}

.pace-center-radar-orange .pace .pace-activity {
  border-color: #fd7e14 transparent transparent;
}

.pace-center-radar-orange .pace .pace-activity::before {
  border-color: #fd7e14 transparent transparent;
}

.pace-center-simple-orange .pace {
  background: #1f2d3d;
  border-color: #fd7e14;
}

.pace-center-simple-orange .pace .pace-progress {
  background: #fd7e14;
}

.pace-material-orange .pace {
  color: #fd7e14;
}

.pace-corner-indicator-orange .pace .pace-activity {
  background: #fd7e14;
}

.pace-corner-indicator-orange .pace .pace-activity::after,
.pace-corner-indicator-orange .pace .pace-activity::before {
  border: 5px solid #1f2d3d;
}

.pace-corner-indicator-orange .pace .pace-activity::before {
  border-right-color: rgba(253, 126, 20, 0.2);
  border-left-color: rgba(253, 126, 20, 0.2);
}

.pace-corner-indicator-orange .pace .pace-activity::after {
  border-top-color: rgba(253, 126, 20, 0.2);
  border-bottom-color: rgba(253, 126, 20, 0.2);
}

.pace-fill-left-orange .pace .pace-progress {
  background-color: rgba(253, 126, 20, 0.2);
}

.pace-flash-orange .pace .pace-progress {
  background: #fd7e14;
}

.pace-flash-orange .pace .pace-progress-inner {
  box-shadow: 0 0 10px #fd7e14, 0 0 5px #fd7e14;
}

.pace-flash-orange .pace .pace-activity {
  border-top-color: #fd7e14;
  border-left-color: #fd7e14;
}

.pace-loading-bar-orange .pace .pace-progress {
  background: #fd7e14;
  color: #fd7e14;
  box-shadow: 120px 0 #1f2d3d, 240px 0 #1f2d3d;
}

.pace-loading-bar-orange .pace .pace-activity {
  box-shadow: inset 0 0 0 2px #fd7e14, inset 0 0 0 7px #1f2d3d;
}

.pace-mac-osx-orange .pace .pace-progress {
  background-color: #fd7e14;
  box-shadow: inset -1px 0 #fd7e14, inset 0 -1px #fd7e14, inset 0 2px rgba(31, 45, 61, 0.5), inset 0 6px rgba(31, 45, 61, 0.3);
}

.pace-mac-osx-orange .pace .pace-activity {
  background-image: radial-gradient(rgba(31, 45, 61, 0.65) 0%, rgba(31, 45, 61, 0.15) 100%);
  height: 12px;
}

.pace-progress-color-orange .pace-progress {
  color: #fd7e14;
}

.pace-yellow .pace .pace-progress {
  background: #ffc107;
}

.pace-barber-shop-yellow .pace {
  background: #1f2d3d;
}

.pace-barber-shop-yellow .pace .pace-progress {
  background: #ffc107;
}

.pace-barber-shop-yellow .pace .pace-activity {
  background-image: linear-gradient(45deg, rgba(31, 45, 61, 0.2) 25%, transparent 25%, transparent 50%, rgba(31, 45, 61, 0.2) 50%, rgba(31, 45, 61, 0.2) 75%, transparent 75%, transparent);
}

.pace-big-counter-yellow .pace .pace-progress::after {
  color: rgba(255, 193, 7, 0.2);
}

.pace-bounce-yellow .pace .pace-activity {
  background: #ffc107;
}

.pace-center-atom-yellow .pace-progress {
  height: 100px;
  width: 80px;
}

.pace-center-atom-yellow .pace-progress::before {
  background: #ffc107;
  color: #1f2d3d;
  font-size: .8rem;
  line-height: .7rem;
  padding-top: 17%;
}

.pace-center-atom-yellow .pace-activity {
  border-color: #ffc107;
}

.pace-center-atom-yellow .pace-activity::after, .pace-center-atom-yellow .pace-activity::before {
  border-color: #ffc107;
}

.pace-center-circle-yellow .pace .pace-progress {
  background: rgba(255, 193, 7, 0.8);
  color: #1f2d3d;
}

.pace-center-radar-yellow .pace .pace-activity {
  border-color: #ffc107 transparent transparent;
}

.pace-center-radar-yellow .pace .pace-activity::before {
  border-color: #ffc107 transparent transparent;
}

.pace-center-simple-yellow .pace {
  background: #1f2d3d;
  border-color: #ffc107;
}

.pace-center-simple-yellow .pace .pace-progress {
  background: #ffc107;
}

.pace-material-yellow .pace {
  color: #ffc107;
}

.pace-corner-indicator-yellow .pace .pace-activity {
  background: #ffc107;
}

.pace-corner-indicator-yellow .pace .pace-activity::after,
.pace-corner-indicator-yellow .pace .pace-activity::before {
  border: 5px solid #1f2d3d;
}

.pace-corner-indicator-yellow .pace .pace-activity::before {
  border-right-color: rgba(255, 193, 7, 0.2);
  border-left-color: rgba(255, 193, 7, 0.2);
}

.pace-corner-indicator-yellow .pace .pace-activity::after {
  border-top-color: rgba(255, 193, 7, 0.2);
  border-bottom-color: rgba(255, 193, 7, 0.2);
}

.pace-fill-left-yellow .pace .pace-progress {
  background-color: rgba(255, 193, 7, 0.2);
}

.pace-flash-yellow .pace .pace-progress {
  background: #ffc107;
}

.pace-flash-yellow .pace .pace-progress-inner {
  box-shadow: 0 0 10px #ffc107, 0 0 5px #ffc107;
}

.pace-flash-yellow .pace .pace-activity {
  border-top-color: #ffc107;
  border-left-color: #ffc107;
}

.pace-loading-bar-yellow .pace .pace-progress {
  background: #ffc107;
  color: #ffc107;
  box-shadow: 120px 0 #1f2d3d, 240px 0 #1f2d3d;
}

.pace-loading-bar-yellow .pace .pace-activity {
  box-shadow: inset 0 0 0 2px #ffc107, inset 0 0 0 7px #1f2d3d;
}

.pace-mac-osx-yellow .pace .pace-progress {
  background-color: #ffc107;
  box-shadow: inset -1px 0 #ffc107, inset 0 -1px #ffc107, inset 0 2px rgba(31, 45, 61, 0.5), inset 0 6px rgba(31, 45, 61, 0.3);
}

.pace-mac-osx-yellow .pace .pace-activity {
  background-image: radial-gradient(rgba(31, 45, 61, 0.65) 0%, rgba(31, 45, 61, 0.15) 100%);
  height: 12px;
}

.pace-progress-color-yellow .pace-progress {
  color: #ffc107;
}

.pace-green .pace .pace-progress {
  background: #28a745;
}

.pace-barber-shop-green .pace {
  background: #fff;
}

.pace-barber-shop-green .pace .pace-progress {
  background: #28a745;
}

.pace-barber-shop-green .pace .pace-activity {
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);
}

.pace-big-counter-green .pace .pace-progress::after {
  color: rgba(40, 167, 69, 0.2);
}

.pace-bounce-green .pace .pace-activity {
  background: #28a745;
}

.pace-center-atom-green .pace-progress {
  height: 100px;
  width: 80px;
}

.pace-center-atom-green .pace-progress::before {
  background: #28a745;
  color: #fff;
  font-size: .8rem;
  line-height: .7rem;
  padding-top: 17%;
}

.pace-center-atom-green .pace-activity {
  border-color: #28a745;
}

.pace-center-atom-green .pace-activity::after, .pace-center-atom-green .pace-activity::before {
  border-color: #28a745;
}

.pace-center-circle-green .pace .pace-progress {
  background: rgba(40, 167, 69, 0.8);
  color: #fff;
}

.pace-center-radar-green .pace .pace-activity {
  border-color: #28a745 transparent transparent;
}

.pace-center-radar-green .pace .pace-activity::before {
  border-color: #28a745 transparent transparent;
}

.pace-center-simple-green .pace {
  background: #fff;
  border-color: #28a745;
}

.pace-center-simple-green .pace .pace-progress {
  background: #28a745;
}

.pace-material-green .pace {
  color: #28a745;
}

.pace-corner-indicator-green .pace .pace-activity {
  background: #28a745;
}

.pace-corner-indicator-green .pace .pace-activity::after,
.pace-corner-indicator-green .pace .pace-activity::before {
  border: 5px solid #fff;
}

.pace-corner-indicator-green .pace .pace-activity::before {
  border-right-color: rgba(40, 167, 69, 0.2);
  border-left-color: rgba(40, 167, 69, 0.2);
}

.pace-corner-indicator-green .pace .pace-activity::after {
  border-top-color: rgba(40, 167, 69, 0.2);
  border-bottom-color: rgba(40, 167, 69, 0.2);
}

.pace-fill-left-green .pace .pace-progress {
  background-color: rgba(40, 167, 69, 0.2);
}

.pace-flash-green .pace .pace-progress {
  background: #28a745;
}

.pace-flash-green .pace .pace-progress-inner {
  box-shadow: 0 0 10px #28a745, 0 0 5px #28a745;
}

.pace-flash-green .pace .pace-activity {
  border-top-color: #28a745;
  border-left-color: #28a745;
}

.pace-loading-bar-green .pace .pace-progress {
  background: #28a745;
  color: #28a745;
  box-shadow: 120px 0 #fff, 240px 0 #fff;
}

.pace-loading-bar-green .pace .pace-activity {
  box-shadow: inset 0 0 0 2px #28a745, inset 0 0 0 7px #fff;
}

.pace-mac-osx-green .pace .pace-progress {
  background-color: #28a745;
  box-shadow: inset -1px 0 #28a745, inset 0 -1px #28a745, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);
}

.pace-mac-osx-green .pace .pace-activity {
  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);
  height: 12px;
}

.pace-progress-color-green .pace-progress {
  color: #28a745;
}

.pace-teal .pace .pace-progress {
  background: #20c997;
}

.pace-barber-shop-teal .pace {
  background: #fff;
}

.pace-barber-shop-teal .pace .pace-progress {
  background: #20c997;
}

.pace-barber-shop-teal .pace .pace-activity {
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);
}

.pace-big-counter-teal .pace .pace-progress::after {
  color: rgba(32, 201, 151, 0.2);
}

.pace-bounce-teal .pace .pace-activity {
  background: #20c997;
}

.pace-center-atom-teal .pace-progress {
  height: 100px;
  width: 80px;
}

.pace-center-atom-teal .pace-progress::before {
  background: #20c997;
  color: #fff;
  font-size: .8rem;
  line-height: .7rem;
  padding-top: 17%;
}

.pace-center-atom-teal .pace-activity {
  border-color: #20c997;
}

.pace-center-atom-teal .pace-activity::after, .pace-center-atom-teal .pace-activity::before {
  border-color: #20c997;
}

.pace-center-circle-teal .pace .pace-progress {
  background: rgba(32, 201, 151, 0.8);
  color: #fff;
}

.pace-center-radar-teal .pace .pace-activity {
  border-color: #20c997 transparent transparent;
}

.pace-center-radar-teal .pace .pace-activity::before {
  border-color: #20c997 transparent transparent;
}

.pace-center-simple-teal .pace {
  background: #fff;
  border-color: #20c997;
}

.pace-center-simple-teal .pace .pace-progress {
  background: #20c997;
}

.pace-material-teal .pace {
  color: #20c997;
}

.pace-corner-indicator-teal .pace .pace-activity {
  background: #20c997;
}

.pace-corner-indicator-teal .pace .pace-activity::after,
.pace-corner-indicator-teal .pace .pace-activity::before {
  border: 5px solid #fff;
}

.pace-corner-indicator-teal .pace .pace-activity::before {
  border-right-color: rgba(32, 201, 151, 0.2);
  border-left-color: rgba(32, 201, 151, 0.2);
}

.pace-corner-indicator-teal .pace .pace-activity::after {
  border-top-color: rgba(32, 201, 151, 0.2);
  border-bottom-color: rgba(32, 201, 151, 0.2);
}

.pace-fill-left-teal .pace .pace-progress {
  background-color: rgba(32, 201, 151, 0.2);
}

.pace-flash-teal .pace .pace-progress {
  background: #20c997;
}

.pace-flash-teal .pace .pace-progress-inner {
  box-shadow: 0 0 10px #20c997, 0 0 5px #20c997;
}

.pace-flash-teal .pace .pace-activity {
  border-top-color: #20c997;
  border-left-color: #20c997;
}

.pace-loading-bar-teal .pace .pace-progress {
  background: #20c997;
  color: #20c997;
  box-shadow: 120px 0 #fff, 240px 0 #fff;
}

.pace-loading-bar-teal .pace .pace-activity {
  box-shadow: inset 0 0 0 2px #20c997, inset 0 0 0 7px #fff;
}

.pace-mac-osx-teal .pace .pace-progress {
  background-color: #20c997;
  box-shadow: inset -1px 0 #20c997, inset 0 -1px #20c997, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);
}

.pace-mac-osx-teal .pace .pace-activity {
  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);
  height: 12px;
}

.pace-progress-color-teal .pace-progress {
  color: #20c997;
}

.pace-cyan .pace .pace-progress {
  background: #17a2b8;
}

.pace-barber-shop-cyan .pace {
  background: #fff;
}

.pace-barber-shop-cyan .pace .pace-progress {
  background: #17a2b8;
}

.pace-barber-shop-cyan .pace .pace-activity {
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);
}

.pace-big-counter-cyan .pace .pace-progress::after {
  color: rgba(23, 162, 184, 0.2);
}

.pace-bounce-cyan .pace .pace-activity {
  background: #17a2b8;
}

.pace-center-atom-cyan .pace-progress {
  height: 100px;
  width: 80px;
}

.pace-center-atom-cyan .pace-progress::before {
  background: #17a2b8;
  color: #fff;
  font-size: .8rem;
  line-height: .7rem;
  padding-top: 17%;
}

.pace-center-atom-cyan .pace-activity {
  border-color: #17a2b8;
}

.pace-center-atom-cyan .pace-activity::after, .pace-center-atom-cyan .pace-activity::before {
  border-color: #17a2b8;
}

.pace-center-circle-cyan .pace .pace-progress {
  background: rgba(23, 162, 184, 0.8);
  color: #fff;
}

.pace-center-radar-cyan .pace .pace-activity {
  border-color: #17a2b8 transparent transparent;
}

.pace-center-radar-cyan .pace .pace-activity::before {
  border-color: #17a2b8 transparent transparent;
}

.pace-center-simple-cyan .pace {
  background: #fff;
  border-color: #17a2b8;
}

.pace-center-simple-cyan .pace .pace-progress {
  background: #17a2b8;
}

.pace-material-cyan .pace {
  color: #17a2b8;
}

.pace-corner-indicator-cyan .pace .pace-activity {
  background: #17a2b8;
}

.pace-corner-indicator-cyan .pace .pace-activity::after,
.pace-corner-indicator-cyan .pace .pace-activity::before {
  border: 5px solid #fff;
}

.pace-corner-indicator-cyan .pace .pace-activity::before {
  border-right-color: rgba(23, 162, 184, 0.2);
  border-left-color: rgba(23, 162, 184, 0.2);
}

.pace-corner-indicator-cyan .pace .pace-activity::after {
  border-top-color: rgba(23, 162, 184, 0.2);
  border-bottom-color: rgba(23, 162, 184, 0.2);
}

.pace-fill-left-cyan .pace .pace-progress {
  background-color: rgba(23, 162, 184, 0.2);
}

.pace-flash-cyan .pace .pace-progress {
  background: #17a2b8;
}

.pace-flash-cyan .pace .pace-progress-inner {
  box-shadow: 0 0 10px #17a2b8, 0 0 5px #17a2b8;
}

.pace-flash-cyan .pace .pace-activity {
  border-top-color: #17a2b8;
  border-left-color: #17a2b8;
}

.pace-loading-bar-cyan .pace .pace-progress {
  background: #17a2b8;
  color: #17a2b8;
  box-shadow: 120px 0 #fff, 240px 0 #fff;
}

.pace-loading-bar-cyan .pace .pace-activity {
  box-shadow: inset 0 0 0 2px #17a2b8, inset 0 0 0 7px #fff;
}

.pace-mac-osx-cyan .pace .pace-progress {
  background-color: #17a2b8;
  box-shadow: inset -1px 0 #17a2b8, inset 0 -1px #17a2b8, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);
}

.pace-mac-osx-cyan .pace .pace-activity {
  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);
  height: 12px;
}

.pace-progress-color-cyan .pace-progress {
  color: #17a2b8;
}

.pace-white .pace .pace-progress {
  background: #fff;
}

.pace-barber-shop-white .pace {
  background: #1f2d3d;
}

.pace-barber-shop-white .pace .pace-progress {
  background: #fff;
}

.pace-barber-shop-white .pace .pace-activity {
  background-image: linear-gradient(45deg, rgba(31, 45, 61, 0.2) 25%, transparent 25%, transparent 50%, rgba(31, 45, 61, 0.2) 50%, rgba(31, 45, 61, 0.2) 75%, transparent 75%, transparent);
}

.pace-big-counter-white .pace .pace-progress::after {
  color: rgba(255, 255, 255, 0.2);
}

.pace-bounce-white .pace .pace-activity {
  background: #fff;
}

.pace-center-atom-white .pace-progress {
  height: 100px;
  width: 80px;
}

.pace-center-atom-white .pace-progress::before {
  background: #fff;
  color: #1f2d3d;
  font-size: .8rem;
  line-height: .7rem;
  padding-top: 17%;
}

.pace-center-atom-white .pace-activity {
  border-color: #fff;
}

.pace-center-atom-white .pace-activity::after, .pace-center-atom-white .pace-activity::before {
  border-color: #fff;
}

.pace-center-circle-white .pace .pace-progress {
  background: rgba(255, 255, 255, 0.8);
  color: #1f2d3d;
}

.pace-center-radar-white .pace .pace-activity {
  border-color: #fff transparent transparent;
}

.pace-center-radar-white .pace .pace-activity::before {
  border-color: #fff transparent transparent;
}

.pace-center-simple-white .pace {
  background: #1f2d3d;
  border-color: #fff;
}

.pace-center-simple-white .pace .pace-progress {
  background: #fff;
}

.pace-material-white .pace {
  color: #fff;
}

.pace-corner-indicator-white .pace .pace-activity {
  background: #fff;
}

.pace-corner-indicator-white .pace .pace-activity::after,
.pace-corner-indicator-white .pace .pace-activity::before {
  border: 5px solid #1f2d3d;
}

.pace-corner-indicator-white .pace .pace-activity::before {
  border-right-color: rgba(255, 255, 255, 0.2);
  border-left-color: rgba(255, 255, 255, 0.2);
}

.pace-corner-indicator-white .pace .pace-activity::after {
  border-top-color: rgba(255, 255, 255, 0.2);
  border-bottom-color: rgba(255, 255, 255, 0.2);
}

.pace-fill-left-white .pace .pace-progress {
  background-color: rgba(255, 255, 255, 0.2);
}

.pace-flash-white .pace .pace-progress {
  background: #fff;
}

.pace-flash-white .pace .pace-progress-inner {
  box-shadow: 0 0 10px #fff, 0 0 5px #fff;
}

.pace-flash-white .pace .pace-activity {
  border-top-color: #fff;
  border-left-color: #fff;
}

.pace-loading-bar-white .pace .pace-progress {
  background: #fff;
  color: #fff;
  box-shadow: 120px 0 #1f2d3d, 240px 0 #1f2d3d;
}

.pace-loading-bar-white .pace .pace-activity {
  box-shadow: inset 0 0 0 2px #fff, inset 0 0 0 7px #1f2d3d;
}

.pace-mac-osx-white .pace .pace-progress {
  background-color: #fff;
  box-shadow: inset -1px 0 #fff, inset 0 -1px #fff, inset 0 2px rgba(31, 45, 61, 0.5), inset 0 6px rgba(31, 45, 61, 0.3);
}

.pace-mac-osx-white .pace .pace-activity {
  background-image: radial-gradient(rgba(31, 45, 61, 0.65) 0%, rgba(31, 45, 61, 0.15) 100%);
  height: 12px;
}

.pace-progress-color-white .pace-progress {
  color: #fff;
}

.pace-gray .pace .pace-progress {
  background: #6c757d;
}

.pace-barber-shop-gray .pace {
  background: #fff;
}

.pace-barber-shop-gray .pace .pace-progress {
  background: #6c757d;
}

.pace-barber-shop-gray .pace .pace-activity {
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);
}

.pace-big-counter-gray .pace .pace-progress::after {
  color: rgba(108, 117, 125, 0.2);
}

.pace-bounce-gray .pace .pace-activity {
  background: #6c757d;
}

.pace-center-atom-gray .pace-progress {
  height: 100px;
  width: 80px;
}

.pace-center-atom-gray .pace-progress::before {
  background: #6c757d;
  color: #fff;
  font-size: .8rem;
  line-height: .7rem;
  padding-top: 17%;
}

.pace-center-atom-gray .pace-activity {
  border-color: #6c757d;
}

.pace-center-atom-gray .pace-activity::after, .pace-center-atom-gray .pace-activity::before {
  border-color: #6c757d;
}

.pace-center-circle-gray .pace .pace-progress {
  background: rgba(108, 117, 125, 0.8);
  color: #fff;
}

.pace-center-radar-gray .pace .pace-activity {
  border-color: #6c757d transparent transparent;
}

.pace-center-radar-gray .pace .pace-activity::before {
  border-color: #6c757d transparent transparent;
}

.pace-center-simple-gray .pace {
  background: #fff;
  border-color: #6c757d;
}

.pace-center-simple-gray .pace .pace-progress {
  background: #6c757d;
}

.pace-material-gray .pace {
  color: #6c757d;
}

.pace-corner-indicator-gray .pace .pace-activity {
  background: #6c757d;
}

.pace-corner-indicator-gray .pace .pace-activity::after,
.pace-corner-indicator-gray .pace .pace-activity::before {
  border: 5px solid #fff;
}

.pace-corner-indicator-gray .pace .pace-activity::before {
  border-right-color: rgba(108, 117, 125, 0.2);
  border-left-color: rgba(108, 117, 125, 0.2);
}

.pace-corner-indicator-gray .pace .pace-activity::after {
  border-top-color: rgba(108, 117, 125, 0.2);
  border-bottom-color: rgba(108, 117, 125, 0.2);
}

.pace-fill-left-gray .pace .pace-progress {
  background-color: rgba(108, 117, 125, 0.2);
}

.pace-flash-gray .pace .pace-progress {
  background: #6c757d;
}

.pace-flash-gray .pace .pace-progress-inner {
  box-shadow: 0 0 10px #6c757d, 0 0 5px #6c757d;
}

.pace-flash-gray .pace .pace-activity {
  border-top-color: #6c757d;
  border-left-color: #6c757d;
}

.pace-loading-bar-gray .pace .pace-progress {
  background: #6c757d;
  color: #6c757d;
  box-shadow: 120px 0 #fff, 240px 0 #fff;
}

.pace-loading-bar-gray .pace .pace-activity {
  box-shadow: inset 0 0 0 2px #6c757d, inset 0 0 0 7px #fff;
}

.pace-mac-osx-gray .pace .pace-progress {
  background-color: #6c757d;
  box-shadow: inset -1px 0 #6c757d, inset 0 -1px #6c757d, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);
}

.pace-mac-osx-gray .pace .pace-activity {
  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);
  height: 12px;
}

.pace-progress-color-gray .pace-progress {
  color: #6c757d;
}

.pace-gray-dark .pace .pace-progress {
  background: #343a40;
}

.pace-barber-shop-gray-dark .pace {
  background: #fff;
}

.pace-barber-shop-gray-dark .pace .pace-progress {
  background: #343a40;
}

.pace-barber-shop-gray-dark .pace .pace-activity {
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);
}

.pace-big-counter-gray-dark .pace .pace-progress::after {
  color: rgba(52, 58, 64, 0.2);
}

.pace-bounce-gray-dark .pace .pace-activity {
  background: #343a40;
}

.pace-center-atom-gray-dark .pace-progress {
  height: 100px;
  width: 80px;
}

.pace-center-atom-gray-dark .pace-progress::before {
  background: #343a40;
  color: #fff;
  font-size: .8rem;
  line-height: .7rem;
  padding-top: 17%;
}

.pace-center-atom-gray-dark .pace-activity {
  border-color: #343a40;
}

.pace-center-atom-gray-dark .pace-activity::after, .pace-center-atom-gray-dark .pace-activity::before {
  border-color: #343a40;
}

.pace-center-circle-gray-dark .pace .pace-progress {
  background: rgba(52, 58, 64, 0.8);
  color: #fff;
}

.pace-center-radar-gray-dark .pace .pace-activity {
  border-color: #343a40 transparent transparent;
}

.pace-center-radar-gray-dark .pace .pace-activity::before {
  border-color: #343a40 transparent transparent;
}

.pace-center-simple-gray-dark .pace {
  background: #fff;
  border-color: #343a40;
}

.pace-center-simple-gray-dark .pace .pace-progress {
  background: #343a40;
}

.pace-material-gray-dark .pace {
  color: #343a40;
}

.pace-corner-indicator-gray-dark .pace .pace-activity {
  background: #343a40;
}

.pace-corner-indicator-gray-dark .pace .pace-activity::after,
.pace-corner-indicator-gray-dark .pace .pace-activity::before {
  border: 5px solid #fff;
}

.pace-corner-indicator-gray-dark .pace .pace-activity::before {
  border-right-color: rgba(52, 58, 64, 0.2);
  border-left-color: rgba(52, 58, 64, 0.2);
}

.pace-corner-indicator-gray-dark .pace .pace-activity::after {
  border-top-color: rgba(52, 58, 64, 0.2);
  border-bottom-color: rgba(52, 58, 64, 0.2);
}

.pace-fill-left-gray-dark .pace .pace-progress {
  background-color: rgba(52, 58, 64, 0.2);
}

.pace-flash-gray-dark .pace .pace-progress {
  background: #343a40;
}

.pace-flash-gray-dark .pace .pace-progress-inner {
  box-shadow: 0 0 10px #343a40, 0 0 5px #343a40;
}

.pace-flash-gray-dark .pace .pace-activity {
  border-top-color: #343a40;
  border-left-color: #343a40;
}

.pace-loading-bar-gray-dark .pace .pace-progress {
  background: #343a40;
  color: #343a40;
  box-shadow: 120px 0 #fff, 240px 0 #fff;
}

.pace-loading-bar-gray-dark .pace .pace-activity {
  box-shadow: inset 0 0 0 2px #343a40, inset 0 0 0 7px #fff;
}

.pace-mac-osx-gray-dark .pace .pace-progress {
  background-color: #343a40;
  box-shadow: inset -1px 0 #343a40, inset 0 -1px #343a40, inset 0 2px rgba(255, 255, 255, 0.5), inset 0 6px rgba(255, 255, 255, 0.3);
}

.pace-mac-osx-gray-dark .pace .pace-activity {
  background-image: radial-gradient(rgba(255, 255, 255, 0.65) 0%, rgba(255, 255, 255, 0.15) 100%);
  height: 12px;
}

.pace-progress-color-gray-dark .pace-progress {
  color: #343a40;
}

/**
  * bootstrap-switch - Turn checkboxes and radio buttons into toggle switches.
  *
  * @version v3.4 (MODDED)
  * @homepage https://bttstrp.github.io/bootstrap-switch
  * <AUTHOR> Larentis <<EMAIL>> (http://larentis.eu)
  * @license MIT
  */
.bootstrap-switch {
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  cursor: pointer;
  direction: ltr;
  display: inline-block;
  line-height: .5rem;
  overflow: hidden;
  position: relative;
  text-align: left;
  transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  z-index: 0;
}

.bootstrap-switch .bootstrap-switch-container {
  border-radius: 0.25rem;
  display: inline-block;
  top: 0;
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.bootstrap-switch:focus-within {
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.bootstrap-switch .bootstrap-switch-handle-on,
.bootstrap-switch .bootstrap-switch-handle-off,
.bootstrap-switch .bootstrap-switch-label {
  box-sizing: border-box;
  cursor: pointer;
  display: table-cell;
  font-size: 1rem;
  font-weight: 500;
  line-height: 1.2rem;
  padding: .25rem .5rem;
  vertical-align: middle;
}

.bootstrap-switch .bootstrap-switch-handle-on,
.bootstrap-switch .bootstrap-switch-handle-off {
  text-align: center;
  z-index: 1;
}

.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-default,
.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-default {
  background: #e9ecef;
  color: #1f2d3d;
}

.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-primary,
.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-primary {
  background: #007bff;
  color: #fff;
}

.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-secondary,
.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-secondary {
  background: #6c757d;
  color: #fff;
}

.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-success,
.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-success {
  background: #28a745;
  color: #fff;
}

.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-info,
.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-info {
  background: #17a2b8;
  color: #fff;
}

.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-warning,
.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-warning {
  background: #ffc107;
  color: #1f2d3d;
}

.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-danger,
.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-danger {
  background: #dc3545;
  color: #fff;
}

.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-light,
.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-light {
  background: #f8f9fa;
  color: #1f2d3d;
}

.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-dark,
.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-dark {
  background: #343a40;
  color: #fff;
}

.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-lightblue,
.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-lightblue {
  background: #3c8dbc;
  color: #fff;
}

.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-navy,
.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-navy {
  background: #001f3f;
  color: #fff;
}

.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-olive,
.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-olive {
  background: #3d9970;
  color: #fff;
}

.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-lime,
.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-lime {
  background: #01ff70;
  color: #1f2d3d;
}

.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-fuchsia,
.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-fuchsia {
  background: #f012be;
  color: #fff;
}

.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-maroon,
.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-maroon {
  background: #d81b60;
  color: #fff;
}

.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-blue,
.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-blue {
  background: #007bff;
  color: #fff;
}

.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-indigo,
.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-indigo {
  background: #6610f2;
  color: #fff;
}

.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-purple,
.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-purple {
  background: #6f42c1;
  color: #fff;
}

.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-pink,
.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-pink {
  background: #e83e8c;
  color: #fff;
}

.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-red,
.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-red {
  background: #dc3545;
  color: #fff;
}

.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-orange,
.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-orange {
  background: #fd7e14;
  color: #1f2d3d;
}

.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-yellow,
.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-yellow {
  background: #ffc107;
  color: #1f2d3d;
}

.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-green,
.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-green {
  background: #28a745;
  color: #fff;
}

.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-teal,
.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-teal {
  background: #20c997;
  color: #fff;
}

.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-cyan,
.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-cyan {
  background: #17a2b8;
  color: #fff;
}

.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-white,
.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-white {
  background: #fff;
  color: #1f2d3d;
}

.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-gray,
.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-gray {
  background: #6c757d;
  color: #fff;
}

.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-gray-dark,
.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-gray-dark {
  background: #343a40;
  color: #fff;
}

.bootstrap-switch .bootstrap-switch-handle-on {
  border-bottom-left-radius: 0.1rem;
  border-top-left-radius: 0.1rem;
}

.bootstrap-switch .bootstrap-switch-handle-off {
  border-bottom-right-radius: 0.1rem;
  border-top-right-radius: 0.1rem;
}

.bootstrap-switch input[type='radio'],
.bootstrap-switch input[type='checkbox'] {
  filter: alpha(opacity=0);
  left: 0;
  margin: 0;
  opacity: 0;
  position: absolute;
  top: 0;
  visibility: hidden;
  z-index: -1;
}

.bootstrap-switch.bootstrap-switch-mini .bootstrap-switch-handle-on,
.bootstrap-switch.bootstrap-switch-mini .bootstrap-switch-handle-off,
.bootstrap-switch.bootstrap-switch-mini .bootstrap-switch-label {
  font-size: .875rem;
  line-height: 1.5;
  padding: .1rem .3rem;
}

.bootstrap-switch.bootstrap-switch-small .bootstrap-switch-handle-on,
.bootstrap-switch.bootstrap-switch-small .bootstrap-switch-handle-off,
.bootstrap-switch.bootstrap-switch-small .bootstrap-switch-label {
  font-size: .875rem;
  line-height: 1.5;
  padding: .2rem .4rem;
}

.bootstrap-switch.bootstrap-switch-large .bootstrap-switch-handle-on,
.bootstrap-switch.bootstrap-switch-large .bootstrap-switch-handle-off,
.bootstrap-switch.bootstrap-switch-large .bootstrap-switch-label {
  font-size: 1.25rem;
  line-height: 1.3333333rem;
  padding: .3rem .5rem;
}

.bootstrap-switch.bootstrap-switch-disabled, .bootstrap-switch.bootstrap-switch-readonly, .bootstrap-switch.bootstrap-switch-indeterminate {
  cursor: default;
}

.bootstrap-switch.bootstrap-switch-disabled .bootstrap-switch-handle-on,
.bootstrap-switch.bootstrap-switch-disabled .bootstrap-switch-handle-off,
.bootstrap-switch.bootstrap-switch-disabled .bootstrap-switch-label, .bootstrap-switch.bootstrap-switch-readonly .bootstrap-switch-handle-on,
.bootstrap-switch.bootstrap-switch-readonly .bootstrap-switch-handle-off,
.bootstrap-switch.bootstrap-switch-readonly .bootstrap-switch-label, .bootstrap-switch.bootstrap-switch-indeterminate .bootstrap-switch-handle-on,
.bootstrap-switch.bootstrap-switch-indeterminate .bootstrap-switch-handle-off,
.bootstrap-switch.bootstrap-switch-indeterminate .bootstrap-switch-label {
  cursor: default;
  filter: alpha(opacity=50);
  opacity: .5;
}

.bootstrap-switch.bootstrap-switch-animate .bootstrap-switch-container {
  transition: margin-left .5s;
}

.bootstrap-switch.bootstrap-switch-inverse .bootstrap-switch-handle-on {
  border-radius: 0 0.1rem 0.1rem 0;
}

.bootstrap-switch.bootstrap-switch-inverse .bootstrap-switch-handle-off {
  border-radius: 0.1rem 0 0 0.1rem;
}

.bootstrap-switch.bootstrap-switch-on .bootstrap-switch-label,
.bootstrap-switch.bootstrap-switch-inverse.bootstrap-switch-off .bootstrap-switch-label {
  border-bottom-right-radius: 0.1rem;
  border-top-right-radius: 0.1rem;
}

.bootstrap-switch.bootstrap-switch-off .bootstrap-switch-label,
.bootstrap-switch.bootstrap-switch-inverse.bootstrap-switch-on .bootstrap-switch-label {
  border-bottom-left-radius: 0.1rem;
  border-top-left-radius: 0.1rem;
}

.dark-mode .bootstrap-switch {
  border-color: #6c757d;
}

.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-default,
.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-default {
  background-color: #3a4047;
  color: #fff;
  border-color: #454d55;
}

.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-primary,
.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-primary {
  background: #3f6791;
  color: #fff;
}

.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-secondary,
.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-secondary {
  background: #6c757d;
  color: #fff;
}

.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-success,
.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-success {
  background: #00bc8c;
  color: #fff;
}

.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-info,
.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-info {
  background: #3498db;
  color: #fff;
}

.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-warning,
.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-warning {
  background: #f39c12;
  color: #1f2d3d;
}

.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-danger,
.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-danger {
  background: #e74c3c;
  color: #fff;
}

.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-light,
.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-light {
  background: #f8f9fa;
  color: #1f2d3d;
}

.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-dark,
.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-dark {
  background: #343a40;
  color: #fff;
}

.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-lightblue,
.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-lightblue {
  background: #86bad8;
  color: #1f2d3d;
}

.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-navy,
.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-navy {
  background: #002c59;
  color: #fff;
}

.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-olive,
.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-olive {
  background: #74c8a3;
  color: #1f2d3d;
}

.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-lime,
.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-lime {
  background: #67ffa9;
  color: #1f2d3d;
}

.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-fuchsia,
.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-fuchsia {
  background: #f672d8;
  color: #1f2d3d;
}

.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-maroon,
.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-maroon {
  background: #ed6c9b;
  color: #1f2d3d;
}

.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-blue,
.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-blue {
  background: #3f6791;
  color: #fff;
}

.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-indigo,
.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-indigo {
  background: #6610f2;
  color: #fff;
}

.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-purple,
.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-purple {
  background: #6f42c1;
  color: #fff;
}

.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-pink,
.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-pink {
  background: #e83e8c;
  color: #fff;
}

.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-red,
.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-red {
  background: #e74c3c;
  color: #fff;
}

.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-orange,
.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-orange {
  background: #fd7e14;
  color: #1f2d3d;
}

.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-yellow,
.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-yellow {
  background: #f39c12;
  color: #1f2d3d;
}

.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-green,
.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-green {
  background: #00bc8c;
  color: #fff;
}

.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-teal,
.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-teal {
  background: #20c997;
  color: #fff;
}

.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-cyan,
.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-cyan {
  background: #3498db;
  color: #fff;
}

.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-white,
.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-white {
  background: #fff;
  color: #1f2d3d;
}

.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-gray,
.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-gray {
  background: #6c757d;
  color: #fff;
}

.dark-mode .bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-gray-dark,
.dark-mode .bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-gray-dark {
  background: #343a40;
  color: #fff;
}

.dark-mode .daterangepicker {
  background-color: #3f474e;
  border: inherit;
}

.dark-mode .daterangepicker::before, .dark-mode .daterangepicker::after {
  border-bottom-color: #3f474e;
}

.dark-mode .daterangepicker td.available:hover,
.dark-mode .daterangepicker th.available:hover {
  background-color: #3f474e;
}

.dark-mode .daterangepicker td.in-range {
  background-color: #4b545c;
  color: #fff;
}

.dark-mode .daterangepicker td.off,
.dark-mode .daterangepicker td.off.in-range,
.dark-mode .daterangepicker td.off.start-date,
.dark-mode .daterangepicker td.off.end-date {
  background-color: #292d32;
  color: #fff;
}

.dark-mode .daterangepicker .ranges li:hover {
  background-color: #343a40;
}

.dark-mode .daterangepicker.show-ranges.ltr .drp-calendar {
  border-color: #4b545c;
}

.dark-mode .daterangepicker.show-ranges.ltr .drp-calendar.left, .dark-mode .daterangepicker.show-ranges.ltr .drp-calendar.right {
  border-color: #4b545c;
  padding-top: 0;
}

.dark-mode .daterangepicker .drp-buttons {
  border-color: #4b545c;
}

.dark-mode .daterangepicker .calendar-table {
  background-color: #343a40;
  border-color: #4b545c;
}

.dark-mode .daterangepicker .calendar-table th,
.dark-mode .daterangepicker .calendar-table td {
  color: #fff;
}

.dark-mode .daterangepicker .calendar-table .next span,
.dark-mode .daterangepicker .calendar-table .prev span {
  border-color: #fff;
}

.dark-mode .daterangepicker select.hourselect,
.dark-mode .daterangepicker select.minuteselect,
.dark-mode .daterangepicker select.secondselect,
.dark-mode .daterangepicker select.ampmselect {
  background-color: #343a40;
  border-color: #4b545c;
}

.jqstooltip {
  height: auto !important;
  padding: 5px !important;
  width: auto !important;
}

.connectedSortable {
  min-height: 100px;
}

.ui-helper-hidden-accessible {
  border: 0;
  clip: rect(0 0 0 0);
  height: 1px;
  margin: -1px;
  overflow: hidden;
  padding: 0;
  position: absolute;
  width: 1px;
}

.sort-highlight {
  background: #f8f9fa;
  border: 1px dashed #dee2e6;
  margin-bottom: 10px;
}

.chart {
  overflow: hidden;
  position: relative;
}

.dark-mode .irs--flat .irs-line {
  background-color: #4b545c;
}

.dark-mode .jsgrid-edit-row > .jsgrid-cell,
.dark-mode .jsgrid-filter-row > .jsgrid-cell,
.dark-mode .jsgrid-grid-body, .dark-mode .jsgrid-grid-header,
.dark-mode .jsgrid-header-row > .jsgrid-header-cell,
.dark-mode .jsgrid-insert-row > .jsgrid-cell,
.dark-mode .jsgrid-row > .jsgrid-cell,
.dark-mode .jsgrid-alt-row > .jsgrid-cell {
  border-color: #6c757d;
}

.dark-mode .jsgrid-header-row > .jsgrid-header-cell,
.dark-mode .jsgrid-row > .jsgrid-cell {
  background-color: #343a40;
}

.dark-mode .jsgrid-alt-row > .jsgrid-cell {
  background-color: #3a4047;
}

.dark-mode .jsgrid-selected-row > .jsgrid-cell {
  background-color: #3f474e;
}
/*# sourceMappingURL=adminlte.plugins.css.map */