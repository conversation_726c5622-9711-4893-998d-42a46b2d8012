from django.contrib import admin
from .models import (
    TestProject, TestPlan, TestSuite, TestCase, Build,
    TestAssignment, TestExecution, UserProfile
)


@admin.register(TestProject)
class TestProjectAdmin(admin.ModelAdmin):
    list_display = ['name', 'prefix', 'testlink_id', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'prefix']
    readonly_fields = ['testlink_id', 'created_at', 'updated_at']


@admin.register(TestPlan)
class TestPlanAdmin(admin.ModelAdmin):
    list_display = ['name', 'project', 'testlink_id', 'is_active', 'created_at']
    list_filter = ['project', 'is_active', 'created_at']
    search_fields = ['name', 'project__name']
    readonly_fields = ['testlink_id', 'created_at', 'updated_at']


@admin.register(TestSuite)
class TestSuiteAdmin(admin.ModelAdmin):
    list_display = ['name', 'project', 'parent_suite', 'testlink_id', 'created_at']
    list_filter = ['project', 'created_at']
    search_fields = ['name', 'project__name']
    readonly_fields = ['testlink_id', 'created_at', 'updated_at']


@admin.register(TestCase)
class TestCaseAdmin(admin.ModelAdmin):
    list_display = ['external_id', 'name', 'project', 'test_suite', 'importance', 'status']
    list_filter = ['project', 'test_suite', 'importance', 'execution_type', 'status']
    search_fields = ['name', 'external_id', 'summary']
    readonly_fields = ['testlink_id', 'created_at', 'updated_at']


@admin.register(Build)
class BuildAdmin(admin.ModelAdmin):
    list_display = ['name', 'test_plan', 'testlink_id', 'is_active', 'is_open', 'created_at']
    list_filter = ['test_plan', 'is_active', 'is_open', 'created_at']
    search_fields = ['name', 'test_plan__name']
    readonly_fields = ['testlink_id', 'created_at', 'updated_at']


@admin.register(TestAssignment)
class TestAssignmentAdmin(admin.ModelAdmin):
    list_display = ['test_case', 'assigned_user', 'test_plan', 'assigned_by', 'assigned_at', 'due_date', 'is_active']
    list_filter = ['test_plan', 'assigned_user', 'assigned_by', 'is_active', 'assigned_at']
    search_fields = ['test_case__name', 'assigned_user__username', 'assigned_by__username']
    readonly_fields = ['assigned_at']


@admin.register(TestExecution)
class TestExecutionAdmin(admin.ModelAdmin):
    list_display = ['test_case', 'test_plan', 'build', 'executed_by', 'status', 'executed_at']
    list_filter = ['test_plan', 'build', 'executed_by', 'status', 'executed_at']
    search_fields = ['test_case__name', 'executed_by__username']
    readonly_fields = ['testlink_id', 'created_at', 'updated_at']


@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    list_display = ['user', 'role', 'testlink_username', 'testlink_user_id']
    list_filter = ['role', 'projects']
    search_fields = ['user__username', 'testlink_username']
    filter_horizontal = ['projects']
    readonly_fields = ['created_at', 'updated_at']
