[{"model": "auth.user", "pk": 1, "fields": {"username": "testlead", "first_name": "Test", "last_name": "Lead", "email": "<EMAIL>", "is_staff": false, "is_active": true, "is_superuser": false, "password": "pbkdf2_sha256$600000$test$hash"}}, {"model": "auth.user", "pk": 2, "fields": {"username": "john", "first_name": "<PERSON>", "last_name": "<PERSON><PERSON>", "email": "<EMAIL>", "is_staff": false, "is_active": true, "is_superuser": false, "password": "pbkdf2_sha256$600000$test$hash"}}, {"model": "auth.user", "pk": 3, "fields": {"username": "jane", "first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "is_staff": false, "is_active": true, "is_superuser": false, "password": "pbkdf2_sha256$600000$test$hash"}}, {"model": "testlink_app.testproject", "pk": 1, "fields": {"testlink_id": 1, "name": "E-Commerce Platform", "prefix": "ECP", "description": "Testing for the main e-commerce platform", "is_active": true}}, {"model": "testlink_app.testproject", "pk": 2, "fields": {"testlink_id": 2, "name": "Mobile App", "prefix": "MOB", "description": "Mobile application testing project", "is_active": true}}, {"model": "testlink_app.testplan", "pk": 1, "fields": {"testlink_id": 1, "project": 1, "name": "E-Commerce Platform - Sprint 1", "description": "Test plan for E-Commerce Platform Sprint 1", "is_active": true}}, {"model": "testlink_app.testplan", "pk": 2, "fields": {"testlink_id": 2, "project": 1, "name": "E-Commerce Platform - Regression", "description": "Regression test plan for E-Commerce Platform", "is_active": true}}, {"model": "testlink_app.testsuite", "pk": 1, "fields": {"testlink_id": 1, "project": 1, "name": "User Authentication", "description": "Test suite for User Authentication in E-Commerce Platform"}}, {"model": "testlink_app.testsuite", "pk": 2, "fields": {"testlink_id": 2, "project": 1, "name": "Shopping Cart", "description": "Test suite for Shopping Cart in E-Commerce Platform"}}, {"model": "testlink_app.testcase", "pk": 1, "fields": {"testlink_id": 1, "external_id": "ECP-001", "project": 1, "test_suite": 1, "name": "Login with valid credentials", "summary": "Verify user can login with valid username and password", "preconditions": "User account exists in the system", "steps": "1. Navigate to login page\n2. Enter valid username\n3. Enter valid password\n4. Click login button", "expected_results": "User is successfully logged in and redirected to dashboard", "importance": "HIGH", "execution_type": "MANUAL", "status": "FINAL"}}, {"model": "testlink_app.testcase", "pk": 2, "fields": {"testlink_id": 2, "external_id": "ECP-002", "project": 1, "test_suite": 1, "name": "Login with invalid credentials", "summary": "Verify error message when login with invalid credentials", "preconditions": "User is on login page", "steps": "1. Enter invalid username\n2. En<PERSON> invalid password\n3. Click login button", "expected_results": "Error message is displayed", "importance": "MEDIUM", "execution_type": "MANUAL", "status": "FINAL"}}, {"model": "testlink_app.testcase", "pk": 3, "fields": {"testlink_id": 3, "external_id": "ECP-003", "project": 1, "test_suite": 2, "name": "Add product to cart", "summary": "Verify user can add product to shopping cart", "preconditions": "User is logged in and on product page", "steps": "1. Select product\n2. Choose quantity\n3. <PERSON><PERSON> add to cart", "expected_results": "Product is added to cart successfully", "importance": "HIGH", "execution_type": "MANUAL", "status": "FINAL"}}, {"model": "testlink_app.build", "pk": 1, "fields": {"testlink_id": 1, "test_plan": 1, "name": "v1.0.0", "description": "Build v1.0.0 for E-Commerce Platform - Sprint 1", "is_active": true, "is_open": true}}, {"model": "testlink_app.build", "pk": 2, "fields": {"testlink_id": 2, "test_plan": 2, "name": "v1.0.1", "description": "Build v1.0.1 for E-Commerce Platform - Regression", "is_active": true, "is_open": true}}, {"model": "testlink_app.userprofile", "pk": 1, "fields": {"user": 1, "testlink_user_id": 100, "testlink_username": "testlead", "role": "TEST_LEAD"}}, {"model": "testlink_app.userprofile", "pk": 2, "fields": {"user": 2, "testlink_user_id": 101, "testlink_username": "john", "role": "TESTER"}}, {"model": "testlink_app.userprofile", "pk": 3, "fields": {"user": 3, "testlink_user_id": 102, "testlink_username": "jane", "role": "TESTER"}}]