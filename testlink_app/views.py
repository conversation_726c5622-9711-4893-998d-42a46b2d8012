from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib import messages
from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.http import JsonResponse, HttpResponse
from django.db.models import Q, Count, Avg
from django.utils import timezone
from django.core.paginator import Paginator
from datetime import datetime, timedelta
import json

from .models import (
    TestProject, TestPlan, TestCase, TestAssignment, 
    TestExecution, Build, UserProfile
)
from .forms import (
    TestAssignmentForm, BulkTestAssignmentForm, TestExecutionForm,
    ProjectFilterForm, TestPlanFilterForm, UserFilterForm, 
    DateRangeFilterForm, SyncTestLinkDataForm
)
from .testlink_client import get_testlink_client, TestLinkAPIError


@login_required
def dashboard(request):
    """Main dashboard view"""
    user_profile = getattr(request.user, 'testlink_profile', None)
    
    # Get user's projects
    if user_profile:
        projects = user_profile.projects.filter(is_active=True)
    else:
        projects = TestProject.objects.filter(is_active=True)
    
    # Get user's assignments
    assignments = TestAssignment.objects.filter(
        assigned_user=request.user,
        is_active=True
    ).select_related('test_case', 'test_plan')[:10]
    
    # Get recent executions
    recent_executions = TestExecution.objects.filter(
        executed_by=request.user
    ).select_related('test_case', 'test_plan', 'build')[:10]
    
    # Statistics
    total_assignments = TestAssignment.objects.filter(
        assigned_user=request.user,
        is_active=True
    ).count()
    
    completed_executions = TestExecution.objects.filter(
        executed_by=request.user,
        status__in=['PASSED', 'FAILED', 'BLOCKED', 'SKIPPED']
    ).count()
    
    pending_assignments = total_assignments - completed_executions
    
    context = {
        'projects': projects,
        'assignments': assignments,
        'recent_executions': recent_executions,
        'total_assignments': total_assignments,
        'completed_executions': completed_executions,
        'pending_assignments': pending_assignments,
    }
    
    return render(request, 'testlink_app/dashboard.html', context)


class TestAssignmentListView(LoginRequiredMixin, ListView):
    """List view for test assignments"""
    model = TestAssignment
    template_name = 'testlink_app/assignments/list.html'
    context_object_name = 'assignments'
    paginate_by = 25
    
    def get_queryset(self):
        queryset = TestAssignment.objects.select_related(
            'test_case', 'test_plan', 'assigned_user', 'assigned_by'
        ).filter(is_active=True)
        
        # Filter by project
        project_id = self.request.GET.get('project')
        if project_id:
            queryset = queryset.filter(test_plan__project_id=project_id)
        
        # Filter by test plan
        test_plan_id = self.request.GET.get('test_plan')
        if test_plan_id:
            queryset = queryset.filter(test_plan_id=test_plan_id)
        
        # Filter by assigned user
        user_id = self.request.GET.get('user')
        if user_id:
            queryset = queryset.filter(assigned_user_id=user_id)
        
        # Filter by current user if not admin
        if not self.request.user.is_staff:
            queryset = queryset.filter(assigned_user=self.request.user)
        
        return queryset.order_by('-assigned_at')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['project_filter_form'] = ProjectFilterForm(self.request.GET)
        context['test_plan_filter_form'] = TestPlanFilterForm(self.request.GET)
        context['user_filter_form'] = UserFilterForm(self.request.GET)
        return context


@login_required
def create_assignment(request):
    """Create a new test assignment"""
    if request.method == 'POST':
        form = TestAssignmentForm(request.POST)
        if form.is_valid():
            assignment = form.save(commit=False)
            assignment.assigned_by = request.user
            assignment.save()
            
            # Try to sync with TestLink
            try:
                client = get_testlink_client()
                client.assign_test_case_execution_task(
                    test_plan_id=assignment.test_plan.testlink_id,
                    test_case_external_id=assignment.test_case.external_id,
                    user_id=assignment.assigned_user.testlink_profile.testlink_user_id
                )
                messages.success(request, 'Assignment created and synced with TestLink successfully.')
            except TestLinkAPIError as e:
                messages.warning(request, f'Assignment created but TestLink sync failed: {e}')
            except Exception as e:
                messages.warning(request, f'Assignment created but TestLink sync failed: {e}')
            
            return redirect('testlink_app:assignment_list')
    else:
        form = TestAssignmentForm()
    
    return render(request, 'testlink_app/assignments/create.html', {'form': form})


@login_required
def bulk_assignment(request):
    """Create bulk test assignments"""
    if request.method == 'POST':
        form = BulkTestAssignmentForm(request.POST)
        if form.is_valid():
            test_plan = form.cleaned_data['test_plan']
            test_cases = form.cleaned_data['test_cases']
            assigned_user = form.cleaned_data['assigned_user']
            due_date = form.cleaned_data['due_date']
            notes = form.cleaned_data['notes']
            
            created_count = 0
            for test_case in test_cases:
                assignment, created = TestAssignment.objects.get_or_create(
                    test_plan=test_plan,
                    test_case=test_case,
                    assigned_user=assigned_user,
                    defaults={
                        'assigned_by': request.user,
                        'due_date': due_date,
                        'notes': notes,
                    }
                )
                if created:
                    created_count += 1
            
            messages.success(request, f'Created {created_count} assignments successfully.')
            return redirect('testlink_app:assignment_list')
    else:
        form = BulkTestAssignmentForm()
    
    return render(request, 'testlink_app/assignments/bulk_create.html', {'form': form})


@login_required
def execute_test(request, assignment_id):
    """Execute a test case"""
    assignment = get_object_or_404(TestAssignment, id=assignment_id, assigned_user=request.user)

    if request.method == 'POST':
        form = TestExecutionForm(request.POST, project=assignment.test_plan.project, test_plan=assignment.test_plan)
        if form.is_valid():
            execution = form.save(commit=False)
            execution.executed_by = request.user
            execution.test_plan = assignment.test_plan
            execution.test_case = assignment.test_case
            execution.save()

            # Try to sync with TestLink
            try:
                client = get_testlink_client()
                client.report_test_case_result(
                    test_plan_id=execution.test_plan.testlink_id,
                    test_case_external_id=execution.test_case.external_id,
                    build_id=execution.build.testlink_id,
                    status=execution.status.lower(),
                    notes=execution.execution_notes,
                    user_id=execution.executed_by.testlink_profile.testlink_user_id
                )
                messages.success(request, 'Test execution recorded and synced with TestLink successfully.')
            except TestLinkAPIError as e:
                messages.warning(request, f'Test execution recorded but TestLink sync failed: {e}')
            except Exception as e:
                messages.warning(request, f'Test execution recorded but TestLink sync failed: {e}')

            return redirect('testlink_app:assignment_list')
    else:
        form = TestExecutionForm(
            project=assignment.test_plan.project,
            test_plan=assignment.test_plan,
            initial={
                'test_plan': assignment.test_plan,
                'test_case': assignment.test_case,
            }
        )

    context = {
        'form': form,
        'assignment': assignment,
    }
    return render(request, 'testlink_app/executions/create.html', context)


class TestExecutionListView(LoginRequiredMixin, ListView):
    """List view for test executions"""
    model = TestExecution
    template_name = 'testlink_app/executions/list.html'
    context_object_name = 'executions'
    paginate_by = 25

    def get_queryset(self):
        queryset = TestExecution.objects.select_related(
            'test_case', 'test_plan', 'build', 'executed_by'
        )

        # Filter by project
        project_id = self.request.GET.get('project')
        if project_id:
            queryset = queryset.filter(test_plan__project_id=project_id)

        # Filter by test plan
        test_plan_id = self.request.GET.get('test_plan')
        if test_plan_id:
            queryset = queryset.filter(test_plan_id=test_plan_id)

        # Filter by executed user
        user_id = self.request.GET.get('user')
        if user_id:
            queryset = queryset.filter(executed_by_id=user_id)

        # Filter by status
        status = self.request.GET.get('status')
        if status:
            queryset = queryset.filter(status=status)

        # Filter by date range
        start_date = self.request.GET.get('start_date')
        end_date = self.request.GET.get('end_date')
        if start_date:
            queryset = queryset.filter(executed_at__date__gte=start_date)
        if end_date:
            queryset = queryset.filter(executed_at__date__lte=end_date)

        # Filter by current user if not admin
        if not self.request.user.is_staff:
            queryset = queryset.filter(executed_by=self.request.user)

        return queryset.order_by('-executed_at')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['project_filter_form'] = ProjectFilterForm(self.request.GET)
        context['test_plan_filter_form'] = TestPlanFilterForm(self.request.GET)
        context['user_filter_form'] = UserFilterForm(self.request.GET)
        context['date_filter_form'] = DateRangeFilterForm(self.request.GET)
        context['status_choices'] = TestExecution.EXECUTION_STATUS_CHOICES
        return context


@login_required
def reports_dashboard(request):
    """Reports dashboard view"""
    # Get filter parameters
    project_id = request.GET.get('project')
    test_plan_id = request.GET.get('test_plan')
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')

    # Base queryset
    executions = TestExecution.objects.all()
    assignments = TestAssignment.objects.filter(is_active=True)

    # Apply filters
    if project_id:
        executions = executions.filter(test_plan__project_id=project_id)
        assignments = assignments.filter(test_plan__project_id=project_id)

    if test_plan_id:
        executions = executions.filter(test_plan_id=test_plan_id)
        assignments = assignments.filter(test_plan_id=test_plan_id)

    if start_date:
        executions = executions.filter(executed_at__date__gte=start_date)

    if end_date:
        executions = executions.filter(executed_at__date__lte=end_date)

    # Calculate statistics
    total_assignments = assignments.count()
    total_executions = executions.count()

    execution_stats = executions.values('status').annotate(count=Count('status'))
    status_counts = {stat['status']: stat['count'] for stat in execution_stats}

    # Project statistics
    project_stats = executions.values('test_plan__project__name').annotate(
        total=Count('id'),
        passed=Count('id', filter=Q(status='PASSED')),
        failed=Count('id', filter=Q(status='FAILED')),
        blocked=Count('id', filter=Q(status='BLOCKED')),
    )

    # User statistics
    user_stats = executions.values('executed_by__username').annotate(
        total=Count('id'),
        passed=Count('id', filter=Q(status='PASSED')),
        failed=Count('id', filter=Q(status='FAILED')),
    ).order_by('-total')[:10]

    context = {
        'total_assignments': total_assignments,
        'total_executions': total_executions,
        'status_counts': status_counts,
        'project_stats': project_stats,
        'user_stats': user_stats,
        'project_filter_form': ProjectFilterForm(request.GET),
        'test_plan_filter_form': TestPlanFilterForm(request.GET),
        'date_filter_form': DateRangeFilterForm(request.GET),
    }

    return render(request, 'testlink_app/reports/dashboard.html', context)
