from django import forms
from django.contrib.auth.models import User
from .models import TestProject, TestPlan, TestCase, TestAssignment, TestExecution, Build


class TestAssignmentForm(forms.ModelForm):
    """Form for creating test assignments"""
    
    class Meta:
        model = TestAssignment
        fields = ['test_plan', 'test_case', 'assigned_user', 'due_date', 'notes']
        widgets = {
            'test_plan': forms.Select(attrs={'class': 'form-control select2'}),
            'test_case': forms.Select(attrs={'class': 'form-control select2'}),
            'assigned_user': forms.Select(attrs={'class': 'form-control select2'}),
            'due_date': forms.DateTimeInput(attrs={'class': 'form-control', 'type': 'datetime-local'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }
    
    def __init__(self, *args, **kwargs):
        project = kwargs.pop('project', None)
        super().__init__(*args, **kwargs)
        
        if project:
            self.fields['test_plan'].queryset = TestPlan.objects.filter(project=project, is_active=True)
            self.fields['test_case'].queryset = TestCase.objects.filter(project=project)
        
        # Filter users to only those with testlink profiles
        self.fields['assigned_user'].queryset = User.objects.filter(
            testlink_profile__isnull=False,
            is_active=True
        ).order_by('username')


class BulkTestAssignmentForm(forms.Form):
    """Form for bulk test case assignments"""
    test_plan = forms.ModelChoiceField(
        queryset=TestPlan.objects.filter(is_active=True),
        widget=forms.Select(attrs={'class': 'form-control select2'}),
        empty_label="Select Test Plan"
    )
    test_cases = forms.ModelMultipleChoiceField(
        queryset=TestCase.objects.all(),
        widget=forms.CheckboxSelectMultiple(attrs={'class': 'form-check-input'}),
        required=True
    )
    assigned_user = forms.ModelChoiceField(
        queryset=User.objects.filter(testlink_profile__isnull=False, is_active=True),
        widget=forms.Select(attrs={'class': 'form-control select2'}),
        empty_label="Select User"
    )
    due_date = forms.DateTimeField(
        required=False,
        widget=forms.DateTimeInput(attrs={'class': 'form-control', 'type': 'datetime-local'})
    )
    notes = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={'class': 'form-control', 'rows': 3})
    )
    
    def __init__(self, *args, **kwargs):
        project = kwargs.pop('project', None)
        super().__init__(*args, **kwargs)
        
        if project:
            self.fields['test_plan'].queryset = TestPlan.objects.filter(project=project, is_active=True)
            self.fields['test_cases'].queryset = TestCase.objects.filter(project=project)


class TestExecutionForm(forms.ModelForm):
    """Form for recording test execution results"""
    
    class Meta:
        model = TestExecution
        fields = ['test_plan', 'test_case', 'build', 'status', 'execution_notes', 'execution_duration']
        widgets = {
            'test_plan': forms.Select(attrs={'class': 'form-control select2'}),
            'test_case': forms.Select(attrs={'class': 'form-control select2'}),
            'build': forms.Select(attrs={'class': 'form-control select2'}),
            'status': forms.Select(attrs={'class': 'form-control'}),
            'execution_notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 4}),
            'execution_duration': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'HH:MM:SS'}),
        }
    
    def __init__(self, *args, **kwargs):
        project = kwargs.pop('project', None)
        test_plan = kwargs.pop('test_plan', None)
        super().__init__(*args, **kwargs)
        
        if project:
            self.fields['test_plan'].queryset = TestPlan.objects.filter(project=project, is_active=True)
            self.fields['test_case'].queryset = TestCase.objects.filter(project=project)
        
        if test_plan:
            self.fields['build'].queryset = Build.objects.filter(test_plan=test_plan, is_active=True)
            self.fields['test_case'].queryset = TestCase.objects.filter(
                testassignment__test_plan=test_plan,
                testassignment__is_active=True
            ).distinct()


class ProjectFilterForm(forms.Form):
    """Form for filtering by project"""
    project = forms.ModelChoiceField(
        queryset=TestProject.objects.filter(is_active=True),
        widget=forms.Select(attrs={'class': 'form-control select2'}),
        empty_label="All Projects",
        required=False
    )


class TestPlanFilterForm(forms.Form):
    """Form for filtering by test plan"""
    test_plan = forms.ModelChoiceField(
        queryset=TestPlan.objects.filter(is_active=True),
        widget=forms.Select(attrs={'class': 'form-control select2'}),
        empty_label="All Test Plans",
        required=False
    )
    
    def __init__(self, *args, **kwargs):
        project = kwargs.pop('project', None)
        super().__init__(*args, **kwargs)
        
        if project:
            self.fields['test_plan'].queryset = TestPlan.objects.filter(project=project, is_active=True)


class UserFilterForm(forms.Form):
    """Form for filtering by user"""
    user = forms.ModelChoiceField(
        queryset=User.objects.filter(testlink_profile__isnull=False, is_active=True),
        widget=forms.Select(attrs={'class': 'form-control select2'}),
        empty_label="All Users",
        required=False
    )


class DateRangeFilterForm(forms.Form):
    """Form for filtering by date range"""
    start_date = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'})
    )
    end_date = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'})
    )


class SyncTestLinkDataForm(forms.Form):
    """Form for syncing data from TestLink"""
    project = forms.ModelChoiceField(
        queryset=TestProject.objects.filter(is_active=True),
        widget=forms.Select(attrs={'class': 'form-control select2'}),
        empty_label="Select Project to Sync"
    )
    sync_test_plans = forms.BooleanField(
        required=False,
        initial=True,
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'})
    )
    sync_test_cases = forms.BooleanField(
        required=False,
        initial=True,
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'})
    )
    sync_builds = forms.BooleanField(
        required=False,
        initial=True,
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'})
    )
