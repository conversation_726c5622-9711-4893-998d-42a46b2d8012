from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone


class TestProject(models.Model):
    """Model representing a TestLink project"""
    testlink_id = models.IntegerField(unique=True)
    name = models.CharField(max_length=255)
    prefix = models.CharField(max_length=50)
    description = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.name} ({self.prefix})"

    class Meta:
        ordering = ['name']


class TestPlan(models.Model):
    """Model representing a TestLink test plan"""
    testlink_id = models.IntegerField(unique=True)
    project = models.ForeignKey(TestProject, on_delete=models.CASCADE, related_name='test_plans')
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True)
    is_active = models.<PERSON><PERSON><PERSON><PERSON><PERSON>(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.project.name} - {self.name}"

    class Meta:
        ordering = ['project__name', 'name']


class TestSuite(models.Model):
    """Model representing a TestLink test suite"""
    testlink_id = models.IntegerField(unique=True)
    project = models.ForeignKey(TestProject, on_delete=models.CASCADE, related_name='test_suites')
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True)
    parent_suite = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, related_name='child_suites')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.project.name} - {self.name}"

    class Meta:
        ordering = ['project__name', 'name']


class TestCase(models.Model):
    """Model representing a TestLink test case"""
    testlink_id = models.IntegerField(unique=True)
    external_id = models.CharField(max_length=50, blank=True)
    project = models.ForeignKey(TestProject, on_delete=models.CASCADE, related_name='test_cases')
    test_suite = models.ForeignKey(TestSuite, on_delete=models.CASCADE, related_name='test_cases')
    name = models.CharField(max_length=255)
    summary = models.TextField(blank=True)
    preconditions = models.TextField(blank=True)
    steps = models.TextField(blank=True)
    expected_results = models.TextField(blank=True)
    importance = models.CharField(max_length=10, choices=[
        ('LOW', 'Low'),
        ('MEDIUM', 'Medium'),
        ('HIGH', 'High'),
    ], default='MEDIUM')
    execution_type = models.CharField(max_length=10, choices=[
        ('MANUAL', 'Manual'),
        ('AUTOMATED', 'Automated'),
    ], default='MANUAL')
    status = models.CharField(max_length=20, choices=[
        ('DRAFT', 'Draft'),
        ('READY', 'Ready for Review'),
        ('REVIEW', 'Under Review'),
        ('REWORK', 'Rework Required'),
        ('OBSOLETE', 'Obsolete'),
        ('FUTURE', 'Future'),
        ('FINAL', 'Final'),
    ], default='DRAFT')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.external_id} - {self.name}"

    class Meta:
        ordering = ['external_id', 'name']


class Build(models.Model):
    """Model representing a TestLink build"""
    testlink_id = models.IntegerField(unique=True)
    test_plan = models.ForeignKey(TestPlan, on_delete=models.CASCADE, related_name='builds')
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)
    is_open = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.test_plan.name} - {self.name}"

    class Meta:
        ordering = ['test_plan__name', 'name']


class TestAssignment(models.Model):
    """Model representing test case assignments to users"""
    test_plan = models.ForeignKey(TestPlan, on_delete=models.CASCADE)
    test_case = models.ForeignKey(TestCase, on_delete=models.CASCADE)
    assigned_user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='test_assignments')
    assigned_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='assignments_made')
    assigned_at = models.DateTimeField(auto_now_add=True)
    due_date = models.DateTimeField(null=True, blank=True)
    notes = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return f"{self.test_case.name} assigned to {self.assigned_user.username}"

    class Meta:
        unique_together = ['test_plan', 'test_case', 'assigned_user']
        ordering = ['assigned_at']


class TestExecution(models.Model):
    """Model representing test execution results"""
    EXECUTION_STATUS_CHOICES = [
        ('NOT_RUN', 'Not Run'),
        ('PASSED', 'Passed'),
        ('FAILED', 'Failed'),
        ('BLOCKED', 'Blocked'),
        ('SKIPPED', 'Skipped'),
    ]

    testlink_id = models.IntegerField(null=True, blank=True)
    test_plan = models.ForeignKey(TestPlan, on_delete=models.CASCADE)
    test_case = models.ForeignKey(TestCase, on_delete=models.CASCADE)
    build = models.ForeignKey(Build, on_delete=models.CASCADE)
    executed_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='test_executions')
    status = models.CharField(max_length=20, choices=EXECUTION_STATUS_CHOICES, default='NOT_RUN')
    execution_notes = models.TextField(blank=True)
    execution_duration = models.DurationField(null=True, blank=True)
    executed_at = models.DateTimeField(default=timezone.now)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.test_case.name} - {self.status} by {self.executed_by.username}"

    class Meta:
        ordering = ['-executed_at']


class UserProfile(models.Model):
    """Extended user profile for TestLink integration"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='testlink_profile')
    testlink_user_id = models.IntegerField(null=True, blank=True)
    testlink_username = models.CharField(max_length=100, blank=True)
    role = models.CharField(max_length=20, choices=[
        ('TESTER', 'Tester'),
        ('TEST_LEAD', 'Test Lead'),
        ('ADMIN', 'Administrator'),
    ], default='TESTER')
    projects = models.ManyToManyField(TestProject, blank=True, related_name='team_members')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.username} - {self.role}"

    class Meta:
        ordering = ['user__username']
