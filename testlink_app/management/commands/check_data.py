from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from testlink_app.models import (
    TestProject, TestPlan, TestSuite, TestCase, Build,
    TestAssignment, TestExecution, UserProfile
)


class Command(BaseCommand):
    help = 'Check test data status'

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('TestLink Manager Data Status')
        )
        self.stdout.write('=' * 50)
        
        # Check users
        users_count = User.objects.count()
        profiles_count = UserProfile.objects.count()
        self.stdout.write(f'Users: {users_count}')
        self.stdout.write(f'User Profiles: {profiles_count}')
        
        # Check projects
        projects_count = TestProject.objects.count()
        self.stdout.write(f'Projects: {projects_count}')
        
        # Check test plans
        test_plans_count = TestPlan.objects.count()
        self.stdout.write(f'Test Plans: {test_plans_count}')
        
        # Check test suites
        test_suites_count = TestSuite.objects.count()
        self.stdout.write(f'Test Suites: {test_suites_count}')
        
        # Check test cases
        test_cases_count = TestCase.objects.count()
        self.stdout.write(f'Test Cases: {test_cases_count}')
        
        # Check builds
        builds_count = Build.objects.count()
        self.stdout.write(f'Builds: {builds_count}')
        
        # Check assignments
        assignments_count = TestAssignment.objects.count()
        active_assignments = TestAssignment.objects.filter(is_active=True).count()
        self.stdout.write(f'Assignments: {assignments_count} (Active: {active_assignments})')
        
        # Check executions
        executions_count = TestExecution.objects.count()
        self.stdout.write(f'Executions: {executions_count}')
        
        # Execution status breakdown
        if executions_count > 0:
            self.stdout.write('\nExecution Status Breakdown:')
            statuses = TestExecution.objects.values_list('status', flat=True)
            status_counts = {}
            for status in statuses:
                status_counts[status] = status_counts.get(status, 0) + 1
            
            for status, count in status_counts.items():
                self.stdout.write(f'  {status}: {count}')
        
        # User roles breakdown
        if profiles_count > 0:
            self.stdout.write('\nUser Roles:')
            roles = UserProfile.objects.values_list('role', flat=True)
            role_counts = {}
            for role in roles:
                role_counts[role] = role_counts.get(role, 0) + 1
            
            for role, count in role_counts.items():
                self.stdout.write(f'  {role}: {count}')
        
        # Sample data
        self.stdout.write('\nSample Projects:')
        for project in TestProject.objects.all()[:3]:
            self.stdout.write(f'  - {project.name} ({project.prefix})')
        
        self.stdout.write('\nSample Users:')
        for user in User.objects.filter(testlink_profile__isnull=False)[:5]:
            role = user.testlink_profile.role
            self.stdout.write(f'  - {user.username} ({user.get_full_name()}) - {role}')
        
        self.stdout.write('\n' + '=' * 50)
        
        if all([users_count, projects_count, test_cases_count]):
            self.stdout.write(
                self.style.SUCCESS('✓ Test data looks good!')
            )
        else:
            self.stdout.write(
                self.style.WARNING('⚠ Some data might be missing. Run: python manage.py create_test_data')
            )
