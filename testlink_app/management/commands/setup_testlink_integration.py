from django.core.management.base import BaseCommand
from django.conf import settings
from testlink_app.testlink_client import get_testlink_client, TestLinkAPIError
from testlink_app.models import TestProject, TestPlan, TestCase, UserProfile
from django.contrib.auth.models import User
import time


class Command(BaseCommand):
    help = 'Setup TestLink integration and sync initial data'

    def add_arguments(self, parser):
        parser.add_argument(
            '--api-key',
            type=str,
            help='TestLink API key to use for connection',
        )
        parser.add_argument(
            '--testlink-url',
            type=str,
            help='TestLink URL to connect to',
        )
        parser.add_argument(
            '--test-connection',
            action='store_true',
            help='Only test the connection without syncing data',
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('Setting up TestLink Integration')
        )
        self.stdout.write('=' * 50)

        # Update settings if provided
        if options['api_key']:
            settings.TESTLINK_API_KEY = options['api_key']
            self.stdout.write(f'Using API key: {options["api_key"][:10]}...')

        if options['testlink_url']:
            settings.TESTLINK_URL = options['testlink_url']
            self.stdout.write(f'Using TestLink URL: {options["testlink_url"]}')

        # Test connection
        self.stdout.write('\nTesting TestLink connection...')
        try:
            client = get_testlink_client()
            
            # Test ping
            if client.ping():
                self.stdout.write(
                    self.style.SUCCESS('✓ Connection successful!')
                )
            else:
                self.stdout.write(
                    self.style.ERROR('✗ Ping failed')
                )
                return

        except TestLinkAPIError as e:
            self.stdout.write(
                self.style.ERROR(f'✗ TestLink API Error: {e}')
            )
            self.print_troubleshooting()
            return
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'✗ Connection Error: {e}')
            )
            self.print_troubleshooting()
            return

        if options['test_connection']:
            self.stdout.write(
                self.style.SUCCESS('\nConnection test completed successfully!')
            )
            return

        # Sync data
        self.stdout.write('\nSyncing data from TestLink...')
        try:
            self.sync_projects(client)
            self.sync_users(client)
            self.create_sample_data_in_testlink(client)
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error during sync: {e}')
            )
            return

        self.stdout.write(
            self.style.SUCCESS('\n✓ TestLink integration setup completed!')
        )

    def sync_projects(self, client):
        """Sync projects from TestLink"""
        try:
            projects = client.get_projects()
            
            if not projects:
                self.stdout.write('No projects found in TestLink')
                return

            for project_data in projects:
                project, created = TestProject.objects.update_or_create(
                    testlink_id=int(project_data['id']),
                    defaults={
                        'name': project_data['name'],
                        'prefix': project_data.get('prefix', ''),
                        'description': project_data.get('notes', ''),
                        'is_active': project_data.get('active', '1') == '1',
                    }
                )
                
                if created:
                    self.stdout.write(f'  Created project: {project.name}')
                else:
                    self.stdout.write(f'  Updated project: {project.name}')

        except Exception as e:
            self.stdout.write(f'Error syncing projects: {e}')

    def sync_users(self, client):
        """Sync users from TestLink"""
        try:
            # This is a placeholder as TestLink user API might be limited
            # We'll just ensure our demo users have proper TestLink IDs
            
            demo_users = [
                ('admin', 1),
                ('testlead', 100),
                ('john', 101),
                ('jane', 102),
                ('mike', 103),
                ('sarah', 104),
                ('david', 105),
            ]
            
            for username, testlink_id in demo_users:
                try:
                    user = User.objects.get(username=username)
                    profile = user.testlink_profile
                    profile.testlink_user_id = testlink_id
                    profile.testlink_username = username
                    profile.save()
                    self.stdout.write(f'  Updated user: {username}')
                except User.DoesNotExist:
                    continue

        except Exception as e:
            self.stdout.write(f'Error syncing users: {e}')

    def create_sample_data_in_testlink(self, client):
        """Create sample data in TestLink if needed"""
        try:
            # Check if we have any projects
            projects = client.get_projects()
            
            if not projects:
                self.stdout.write('Creating sample project in TestLink...')
                # Note: Creating projects via API might require admin privileges
                # This is just a placeholder for the concept
                self.stdout.write('  (Project creation via API requires admin access)')
            
            self.stdout.write('Sample data creation completed')

        except Exception as e:
            self.stdout.write(f'Error creating sample data: {e}')

    def print_troubleshooting(self):
        """Print troubleshooting information"""
        self.stdout.write('\n' + '=' * 50)
        self.stdout.write('TROUBLESHOOTING:')
        self.stdout.write('=' * 50)
        self.stdout.write('')
        self.stdout.write('1. Check TestLink is running:')
        self.stdout.write('   curl http://localhost:8080')
        self.stdout.write('')
        self.stdout.write('2. Enable XML-RPC API in TestLink:')
        self.stdout.write('   - Login to TestLink admin')
        self.stdout.write('   - Go to System > Configuration')
        self.stdout.write('   - Enable "Enable TestLink XML-RPC API"')
        self.stdout.write('')
        self.stdout.write('3. Generate API key:')
        self.stdout.write('   - Go to your user profile in TestLink')
        self.stdout.write('   - Generate an API key')
        self.stdout.write('   - Update TESTLINK_API_KEY in settings')
        self.stdout.write('')
        self.stdout.write('4. Check URL format:')
        self.stdout.write('   - Should be: http://localhost:8080/lib/api/xmlrpc/v1/xmlrpc.php')
        self.stdout.write('')
        self.stdout.write('5. Restart services:')
        self.stdout.write('   docker-compose restart web')
        self.stdout.write('')
