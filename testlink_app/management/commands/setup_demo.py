from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.core.management import call_command


class Command(BaseCommand):
    help = 'Setup demo environment with test data'

    def add_arguments(self, parser):
        parser.add_argument(
            '--reset',
            action='store_true',
            help='Reset database and create fresh demo data',
        )

    def handle(self, *args, **options):
        if options['reset']:
            self.stdout.write('Resetting database...')
            call_command('flush', '--noinput')
            call_command('migrate')

        self.stdout.write('Setting up demo environment...')
        
        # Create superuser if not exists
        if not User.objects.filter(username='admin').exists():
            self.stdout.write('Creating admin user...')
            User.objects.create_superuser(
                username='admin',
                email='<EMAIL>',
                password='admin123'
            )
            self.stdout.write(
                self.style.SUCCESS('Admin user created: admin/admin123')
            )

        # Create test data
        self.stdout.write('Creating test data...')
        call_command('create_test_data', '--clear')

        self.stdout.write(
            self.style.SUCCESS('\n' + '='*50)
        )
        self.stdout.write(
            self.style.SUCCESS('Demo environment setup complete!')
        )
        self.stdout.write(
            self.style.SUCCESS('='*50)
        )
        self.stdout.write('\nLogin credentials:')
        self.stdout.write('- Admin: admin/admin123')
        self.stdout.write('- Test Lead: testlead/testpass123')
        self.stdout.write('- Testers: john/testpass123, jane/testpass123, etc.')
        self.stdout.write('\nRun: python manage.py runserver')
        self.stdout.write('Visit: http://localhost:8000')
        self.stdout.write('')
