from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.utils import timezone
from datetime import timedelta
import random

from testlink_app.models import (
    TestProject, TestPlan, TestSuite, TestCase, Build,
    TestAssignment, TestExecution, UserProfile
)


class Command(BaseCommand):
    help = 'Create test data for TestLink Manager'

    def add_arguments(self, parser):
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear existing test data before creating new data',
        )

    def handle(self, *args, **options):
        if options['clear']:
            self.stdout.write('Clearing existing test data...')
            self.clear_test_data()

        self.stdout.write('Creating test data...')
        
        # Create users
        users = self.create_users()
        
        # Create projects
        projects = self.create_projects()
        
        # Create test plans
        test_plans = self.create_test_plans(projects)
        
        # Create test suites
        test_suites = self.create_test_suites(projects)
        
        # Create test cases
        test_cases = self.create_test_cases(projects, test_suites)
        
        # Create builds
        builds = self.create_builds(test_plans)
        
        # Create assignments
        assignments = self.create_assignments(test_plans, test_cases, users)
        
        # Create executions
        self.create_executions(test_plans, test_cases, builds, users)
        
        self.stdout.write(
            self.style.SUCCESS('Successfully created test data!')
        )

    def clear_test_data(self):
        """Clear existing test data"""
        TestExecution.objects.all().delete()
        TestAssignment.objects.all().delete()
        Build.objects.all().delete()
        TestCase.objects.all().delete()
        TestSuite.objects.all().delete()
        TestPlan.objects.all().delete()
        TestProject.objects.all().delete()
        
        # Clear users except superusers
        User.objects.filter(is_superuser=False).delete()

    def create_users(self):
        """Create test users"""
        users = []
        
        # Create test lead
        test_lead = User.objects.create_user(
            username='testlead',
            email='<EMAIL>',
            password='testpass123',
            first_name='Test',
            last_name='Lead'
        )
        profile = UserProfile.objects.get(user=test_lead)
        profile.role = 'TEST_LEAD'
        profile.testlink_user_id = 100
        profile.testlink_username = 'testlead'
        profile.save()
        users.append(test_lead)
        
        # Create testers
        tester_names = [
            ('john', 'John', 'Doe'),
            ('jane', 'Jane', 'Smith'),
            ('mike', 'Mike', 'Johnson'),
            ('sarah', 'Sarah', 'Wilson'),
            ('david', 'David', 'Brown')
        ]
        
        for i, (username, first_name, last_name) in enumerate(tester_names):
            user = User.objects.create_user(
                username=username,
                email=f'{username}@example.com',
                password='testpass123',
                first_name=first_name,
                last_name=last_name
            )
            profile = UserProfile.objects.get(user=user)
            profile.role = 'TESTER'
            profile.testlink_user_id = 101 + i
            profile.testlink_username = username
            profile.save()
            users.append(user)
        
        self.stdout.write(f'Created {len(users)} users')
        return users

    def create_projects(self):
        """Create test projects"""
        projects_data = [
            {
                'name': 'E-Commerce Platform',
                'prefix': 'ECP',
                'description': 'Testing for the main e-commerce platform',
                'testlink_id': 1
            },
            {
                'name': 'Mobile App',
                'prefix': 'MOB',
                'description': 'Mobile application testing project',
                'testlink_id': 2
            },
            {
                'name': 'API Services',
                'prefix': 'API',
                'description': 'Backend API services testing',
                'testlink_id': 3
            }
        ]
        
        projects = []
        for data in projects_data:
            project = TestProject.objects.create(**data)
            projects.append(project)
        
        # Assign users to projects
        for project in projects:
            # Assign all users to all projects for demo
            for user in User.objects.filter(testlink_profile__isnull=False):
                user.testlink_profile.projects.add(project)
        
        self.stdout.write(f'Created {len(projects)} projects')
        return projects

    def create_test_plans(self, projects):
        """Create test plans"""
        test_plans = []
        
        for i, project in enumerate(projects):
            plans_data = [
                {
                    'name': f'{project.name} - Sprint 1',
                    'description': f'Test plan for {project.name} Sprint 1',
                    'testlink_id': (i * 10) + 1
                },
                {
                    'name': f'{project.name} - Sprint 2',
                    'description': f'Test plan for {project.name} Sprint 2',
                    'testlink_id': (i * 10) + 2
                },
                {
                    'name': f'{project.name} - Regression',
                    'description': f'Regression test plan for {project.name}',
                    'testlink_id': (i * 10) + 3
                }
            ]
            
            for plan_data in plans_data:
                plan = TestPlan.objects.create(
                    project=project,
                    **plan_data
                )
                test_plans.append(plan)
        
        self.stdout.write(f'Created {len(test_plans)} test plans')
        return test_plans

    def create_test_suites(self, projects):
        """Create test suites"""
        test_suites = []
        
        suite_names = [
            'User Authentication',
            'Product Management',
            'Shopping Cart',
            'Payment Processing',
            'Order Management',
            'User Profile',
            'Search Functionality',
            'Admin Panel'
        ]
        
        for i, project in enumerate(projects):
            for j, suite_name in enumerate(suite_names[:5]):  # 5 suites per project
                suite = TestSuite.objects.create(
                    testlink_id=(i * 100) + j + 1,
                    project=project,
                    name=suite_name,
                    description=f'Test suite for {suite_name} in {project.name}'
                )
                test_suites.append(suite)
        
        self.stdout.write(f'Created {len(test_suites)} test suites')
        return test_suites

    def create_test_cases(self, projects, test_suites):
        """Create test cases"""
        test_cases = []
        
        test_case_templates = [
            {
                'name': 'Login with valid credentials',
                'summary': 'Verify user can login with valid username and password',
                'preconditions': 'User account exists in the system',
                'steps': '1. Navigate to login page\n2. Enter valid username\n3. Enter valid password\n4. Click login button',
                'expected_results': 'User is successfully logged in and redirected to dashboard',
                'importance': 'HIGH'
            },
            {
                'name': 'Login with invalid credentials',
                'summary': 'Verify error message when login with invalid credentials',
                'preconditions': 'User is on login page',
                'steps': '1. Enter invalid username\n2. Enter invalid password\n3. Click login button',
                'expected_results': 'Error message is displayed',
                'importance': 'MEDIUM'
            },
            {
                'name': 'Add product to cart',
                'summary': 'Verify user can add product to shopping cart',
                'preconditions': 'User is logged in and on product page',
                'steps': '1. Select product\n2. Choose quantity\n3. Click add to cart',
                'expected_results': 'Product is added to cart successfully',
                'importance': 'HIGH'
            },
            {
                'name': 'Remove product from cart',
                'summary': 'Verify user can remove product from cart',
                'preconditions': 'Product exists in cart',
                'steps': '1. Go to cart page\n2. Click remove button\n3. Confirm removal',
                'expected_results': 'Product is removed from cart',
                'importance': 'MEDIUM'
            },
            {
                'name': 'Search for products',
                'summary': 'Verify search functionality works correctly',
                'preconditions': 'User is on homepage',
                'steps': '1. Enter search term\n2. Click search button\n3. Review results',
                'expected_results': 'Relevant products are displayed',
                'importance': 'HIGH'
            }
        ]
        
        case_counter = 1
        for suite in test_suites:
            for i, template in enumerate(test_case_templates):
                test_case = TestCase.objects.create(
                    testlink_id=case_counter,
                    external_id=f'{suite.project.prefix}-{case_counter:03d}',
                    project=suite.project,
                    test_suite=suite,
                    name=f"{template['name']} - {suite.name}",
                    summary=template['summary'],
                    preconditions=template['preconditions'],
                    steps=template['steps'],
                    expected_results=template['expected_results'],
                    importance=template['importance'],
                    execution_type=random.choice(['MANUAL', 'AUTOMATED']),
                    status=random.choice(['DRAFT', 'READY', 'FINAL'])
                )
                test_cases.append(test_case)
                case_counter += 1
        
        self.stdout.write(f'Created {len(test_cases)} test cases')
        return test_cases

    def create_builds(self, test_plans):
        """Create builds"""
        builds = []
        
        for plan in test_plans:
            build_names = ['v1.0.0', 'v1.0.1', 'v1.1.0', 'v1.1.1']
            
            for i, build_name in enumerate(build_names[:2]):  # 2 builds per plan
                build = Build.objects.create(
                    testlink_id=(plan.testlink_id * 10) + i + 1,
                    test_plan=plan,
                    name=build_name,
                    description=f'Build {build_name} for {plan.name}',
                    is_active=True,
                    is_open=i == 1  # Only latest build is open
                )
                builds.append(build)
        
        self.stdout.write(f'Created {len(builds)} builds')
        return builds

    def create_assignments(self, test_plans, test_cases, users):
        """Create test assignments"""
        assignments = []
        testers = [u for u in users if u.testlink_profile.role == 'TESTER']
        
        for plan in test_plans:
            # Get test cases for this project
            plan_test_cases = [tc for tc in test_cases if tc.project == plan.project]
            
            # Assign random test cases to random testers
            for test_case in random.sample(plan_test_cases, min(10, len(plan_test_cases))):
                assigned_user = random.choice(testers)
                assigned_by = random.choice([u for u in users if u.testlink_profile.role == 'TEST_LEAD'])
                
                # Random due date (1-30 days from now)
                due_date = timezone.now() + timedelta(days=random.randint(1, 30))
                
                assignment = TestAssignment.objects.create(
                    test_plan=plan,
                    test_case=test_case,
                    assigned_user=assigned_user,
                    assigned_by=assigned_by,
                    due_date=due_date,
                    notes=f'Assignment for {test_case.name}',
                    is_active=True
                )
                assignments.append(assignment)
        
        self.stdout.write(f'Created {len(assignments)} assignments')
        return assignments

    def create_executions(self, test_plans, test_cases, builds, users):
        """Create test executions"""
        executions = []
        testers = [u for u in users if u.testlink_profile.role == 'TESTER']
        statuses = ['PASSED', 'FAILED', 'BLOCKED', 'SKIPPED', 'NOT_RUN']
        status_weights = [50, 20, 10, 10, 10]  # More passed tests
        
        # Create executions for some assignments
        assignments = TestAssignment.objects.all()
        
        for assignment in random.sample(list(assignments), min(50, len(assignments))):
            # Find a build for this test plan
            plan_builds = [b for b in builds if b.test_plan == assignment.test_plan]
            if not plan_builds:
                continue
                
            build = random.choice(plan_builds)
            status = random.choices(statuses, weights=status_weights)[0]
            
            # Random execution time (1-7 days ago)
            executed_at = timezone.now() - timedelta(days=random.randint(1, 7))
            
            # Random duration (5 minutes to 2 hours)
            duration = timedelta(minutes=random.randint(5, 120))
            
            notes = self.generate_execution_notes(status)
            
            execution = TestExecution.objects.create(
                test_plan=assignment.test_plan,
                test_case=assignment.test_case,
                build=build,
                executed_by=assignment.assigned_user,
                status=status,
                execution_notes=notes,
                execution_duration=duration,
                executed_at=executed_at
            )
            executions.append(execution)
        
        self.stdout.write(f'Created {len(executions)} executions')
        return executions

    def generate_execution_notes(self, status):
        """Generate realistic execution notes based on status"""
        notes_templates = {
            'PASSED': [
                'Test executed successfully. All steps completed as expected.',
                'Functionality works correctly. No issues found.',
                'Test passed. UI behaves as designed.',
                'All assertions passed. Feature working properly.'
            ],
            'FAILED': [
                'Test failed at step 3. Error message not displayed.',
                'Expected result not achieved. Button click not working.',
                'Validation failed. Incorrect data accepted.',
                'UI element not found. Page layout issue.'
            ],
            'BLOCKED': [
                'Cannot proceed due to server downtime.',
                'Test environment not available.',
                'Dependent feature not implemented yet.',
                'Database connection issue preventing test execution.'
            ],
            'SKIPPED': [
                'Test skipped due to time constraints.',
                'Feature not ready for testing.',
                'Skipped as per test plan decision.',
                'Test case marked as out of scope.'
            ],
            'NOT_RUN': [
                'Test not executed yet.',
                'Pending execution.',
                'Scheduled for next iteration.',
                'Awaiting test data setup.'
            ]
        }
        
        return random.choice(notes_templates.get(status, ['Test execution completed.']))
