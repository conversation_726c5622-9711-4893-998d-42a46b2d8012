"""
TestLink API Client for XML-RPC communication
"""
import xmlrpc.client
import logging
from django.conf import settings
from typing import Dict, List, Optional, Any


logger = logging.getLogger(__name__)


class TestLinkAPIError(Exception):
    """Custom exception for TestLink API errors"""
    pass


class TestLinkClient:
    """
    Client for communicating with Test<PERSON>ink via XML-RPC API
    """
    
    def __init__(self, url: str = None, api_key: str = None):
        self.url = url or settings.TESTLINK_URL
        self.api_key = api_key or settings.TESTLINK_API_KEY
        
        if not self.url or not self.api_key:
            raise TestLinkAPIError("TestLink URL and API key must be configured")
        
        try:
            self.server = xmlrpc.client.ServerProxy(self.url)
        except Exception as e:
            raise TestLinkAPIError(f"Failed to connect to TestLink server: {e}")
    
    def _call_api(self, method_name: str, params: Dict = None) -> Any:
        """
        Make an API call to TestLink
        """
        if params is None:
            params = {}
        
        # Add API key to all requests
        params['devKey'] = self.api_key
        
        try:
            method = getattr(self.server.tl, method_name)
            result = method(params)
            
            # Check for API errors
            if isinstance(result, dict) and 'code' in result and result['code'] != 200:
                raise TestLinkAPIError(f"API Error: {result.get('message', 'Unknown error')}")
            
            return result
        except xmlrpc.client.Fault as e:
            logger.error(f"XML-RPC Fault in {method_name}: {e}")
            raise TestLinkAPIError(f"XML-RPC Fault: {e}")
        except Exception as e:
            logger.error(f"Error calling {method_name}: {e}")
            raise TestLinkAPIError(f"API call failed: {e}")
    
    def ping(self) -> bool:
        """
        Test connection to TestLink
        """
        try:
            result = self._call_api('ping')
            return result == 'Hello!'
        except Exception:
            return False
    
    def get_projects(self) -> List[Dict]:
        """
        Get all projects from TestLink
        """
        return self._call_api('getProjects')
    
    def get_project_test_plans(self, project_id: int) -> List[Dict]:
        """
        Get test plans for a project
        """
        return self._call_api('getProjectTestPlans', {'testprojectid': project_id})
    
    def get_test_plan_by_id(self, test_plan_id: int) -> Dict:
        """
        Get test plan details by ID
        """
        result = self._call_api('getTestPlanByID', {'testplanid': test_plan_id})
        return result[0] if isinstance(result, list) and result else result
    
    def get_test_suites_for_test_plan(self, test_plan_id: int) -> List[Dict]:
        """
        Get test suites for a test plan
        """
        return self._call_api('getTestSuitesForTestPlan', {'testplanid': test_plan_id})
    
    def get_test_cases_for_test_suite(self, test_suite_id: int, deep: bool = True) -> List[Dict]:
        """
        Get test cases for a test suite
        """
        return self._call_api('getTestCasesForTestSuite', {
            'testsuiteid': test_suite_id,
            'deep': deep,
            'details': 'full'
        })
    
    def get_test_case_by_id(self, test_case_id: int) -> Dict:
        """
        Get test case details by ID
        """
        result = self._call_api('getTestCase', {'testcaseid': test_case_id})
        return result[0] if isinstance(result, list) and result else result
    
    def get_builds_for_test_plan(self, test_plan_id: int) -> List[Dict]:
        """
        Get builds for a test plan
        """
        return self._call_api('getBuildsForTestPlan', {'testplanid': test_plan_id})
    
    def get_test_plan_platforms(self, test_plan_id: int) -> List[Dict]:
        """
        Get platforms for a test plan
        """
        return self._call_api('getTestPlanPlatforms', {'testplanid': test_plan_id})
    
    def assign_test_case_execution_task(self, test_plan_id: int, test_case_external_id: str, 
                                       user_id: int, build_id: int = None, platform_id: int = None) -> Dict:
        """
        Assign a test case to a user for execution
        """
        params = {
            'testplanid': test_plan_id,
            'testcaseexternalid': test_case_external_id,
            'userid': user_id
        }
        
        if build_id:
            params['buildid'] = build_id
        if platform_id:
            params['platformid'] = platform_id
            
        return self._call_api('assignTestCaseExecutionTask', params)
    
    def report_test_case_result(self, test_plan_id: int, test_case_external_id: str,
                               build_id: int, status: str, notes: str = "", 
                               user_id: int = None, platform_id: int = None) -> Dict:
        """
        Report test case execution result
        """
        params = {
            'testplanid': test_plan_id,
            'testcaseexternalid': test_case_external_id,
            'buildid': build_id,
            'status': status,
            'notes': notes
        }
        
        if user_id:
            params['user'] = user_id
        if platform_id:
            params['platformid'] = platform_id
            
        return self._call_api('reportTCResult', params)
    
    def get_execution_results(self, test_plan_id: int, test_case_id: int = None,
                             build_id: int = None, platform_id: int = None) -> List[Dict]:
        """
        Get execution results for test cases
        """
        params = {'testplanid': test_plan_id}
        
        if test_case_id:
            params['testcaseid'] = test_case_id
        if build_id:
            params['buildid'] = build_id
        if platform_id:
            params['platformid'] = platform_id
            
        return self._call_api('getLastExecutionResult', params)
    
    def get_test_case_assigned_tester(self, test_plan_id: int, test_case_external_id: str,
                                     build_id: int = None, platform_id: int = None) -> Dict:
        """
        Get assigned tester for a test case
        """
        params = {
            'testplanid': test_plan_id,
            'testcaseexternalid': test_case_external_id
        }
        
        if build_id:
            params['buildid'] = build_id
        if platform_id:
            params['platformid'] = platform_id
            
        return self._call_api('getTestCaseAssignedTester', params)
    
    def get_users(self) -> List[Dict]:
        """
        Get all users from TestLink
        """
        return self._call_api('getUserByID', {'userid': 0})  # Special case to get all users


# Singleton instance
testlink_client = None

def get_testlink_client() -> TestLinkClient:
    """
    Get singleton TestLink client instance
    """
    global testlink_client
    if testlink_client is None:
        testlink_client = TestLinkClient()
    return testlink_client
