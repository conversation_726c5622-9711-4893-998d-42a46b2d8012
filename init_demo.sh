#!/bin/bash

# TestLink Manager Demo Initialization Script

echo "=================================================="
echo "TestLink Manager - Demo Initialization"
echo "=================================================="

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "Creating virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
echo "Activating virtual environment..."
source venv/bin/activate

# Install requirements
echo "Installing requirements..."
pip install -r requirements.txt

# Run migrations
echo "Running database migrations..."
python manage.py makemigrations
python manage.py migrate

# Create superuser
echo "Creating admin user..."
python manage.py shell << EOF
from django.contrib.auth.models import User
if not User.objects.filter(username='admin').exists():
    User.objects.create_superuser('admin', '<EMAIL>', 'admin123')
    print('Admin user created: admin/admin123')
else:
    print('Admin user already exists')
EOF

# Create test data
echo "Creating test data..."
python manage.py create_test_data --clear

# Collect static files
echo "Collecting static files..."
python manage.py collectstatic --noinput

echo ""
echo "=================================================="
echo "Demo setup complete!"
echo "=================================================="
echo ""
echo "Login credentials:"
echo "- Admin: admin/admin123"
echo "- Test Lead: testlead/testpass123"
echo "- Testers: john/testpass123, jane/testpass123, mike/testpass123"
echo ""
echo "To start the server:"
echo "python manage.py runserver"
echo ""
echo "Then visit: http://localhost:8000"
echo ""
