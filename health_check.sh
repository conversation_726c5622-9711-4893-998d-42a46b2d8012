#!/bin/bash

# Health Check Script for TestLink Manager + TestLink

echo "=================================================="
echo "Health Check - TestLink Manager + TestLink"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to check service
check_service() {
    local service_name=$1
    local url=$2
    local expected_code=${3:-200}
    
    echo -n "Checking $service_name... "
    
    response=$(curl -s -o /dev/null -w "%{http_code}" "$url" 2>/dev/null)
    
    if [ "$response" = "$expected_code" ]; then
        echo -e "${GREEN}✓ OK${NC} (HTTP $response)"
        return 0
    else
        echo -e "${RED}✗ FAILED${NC} (HTTP $response)"
        return 1
    fi
}

# Function to check database connection
check_database() {
    local db_name=$1
    local host=$2
    local port=$3
    local user=$4
    
    echo -n "Checking $db_name database... "
    
    if nc -z "$host" "$port" 2>/dev/null; then
        echo -e "${GREEN}✓ OK${NC} (Port $port open)"
        return 0
    else
        echo -e "${RED}✗ FAILED${NC} (Port $port closed)"
        return 1
    fi
}

# Check Docker services
echo "Docker Services Status:"
echo "----------------------"
docker-compose ps

echo ""
echo "Service Health Checks:"
echo "---------------------"

# Check databases
check_database "PostgreSQL" "localhost" "5432" "testlink_user"
check_database "MySQL" "localhost" "3306" "testlink"

echo ""

# Check web services
check_service "TestLink Manager" "http://localhost:8000"
check_service "TestLink" "http://localhost:8080"
check_service "Nginx Health" "http://localhost:8090/health"

echo ""

# Check TestLink Manager API
echo "TestLink Manager API Checks:"
echo "----------------------------"
check_service "Dashboard" "http://localhost:8000/"
check_service "Admin Panel" "http://localhost:8000/admin/"
check_service "Login Page" "http://localhost:8000/login/"

echo ""

# Check TestLink API
echo "TestLink API Checks:"
echo "-------------------"
check_service "TestLink Home" "http://localhost:8080/"
check_service "TestLink API" "http://localhost:8080/lib/api/xmlrpc/v1/xmlrpc.php"

echo ""

# Check logs for errors
echo "Recent Error Logs:"
echo "-----------------"
echo "TestLink Manager errors (last 10 lines):"
docker-compose logs --tail=10 web 2>/dev/null | grep -i error || echo "No recent errors"

echo ""
echo "TestLink errors (last 10 lines):"
docker-compose logs --tail=10 testlink 2>/dev/null | grep -i error || echo "No recent errors"

echo ""

# Memory and CPU usage
echo "Resource Usage:"
echo "--------------"
docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}"

echo ""

# Disk usage
echo "Disk Usage:"
echo "----------"
docker system df

echo ""

# Integration test
echo "Integration Test:"
echo "----------------"
echo "Testing TestLink Manager to TestLink connection..."

# Try to connect to TestLink Manager and check if it can reach TestLink
response=$(curl -s "http://localhost:8000/api/test-connection" 2>/dev/null || echo "Connection test endpoint not available")
echo "Connection test result: $response"

echo ""
echo "=================================================="
echo "Health Check Complete"
echo "=================================================="

# Summary
echo ""
echo "Quick Access URLs:"
echo "- TestLink Manager: http://localhost:8000"
echo "- TestLink: http://localhost:8080"
echo "- Nginx Health: http://localhost:8090/health"
echo ""
echo "Database Connections:"
echo "- PostgreSQL: localhost:5432"
echo "- MySQL: localhost:3306"
echo ""
echo "To view logs: docker-compose logs [service_name]"
echo "To restart services: docker-compose restart"
echo "To stop services: docker-compose down"
echo ""
