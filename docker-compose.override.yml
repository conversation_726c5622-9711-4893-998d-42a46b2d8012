# Development overrides for docker-compose
version: '3.8'

services:
  web:
    environment:
      - DEBUG=True
      - TESTLINK_URL=http://localhost:8080/lib/api/xmlrpc/v1/xmlrpc.php
    volumes:
      - .:/app
    command: >
      sh -c "python manage.py migrate &&
             python manage.py collectstatic --noinput &&
             python manage.py setup_demo &&
             python manage.py runserver 0.0.0.0:8000"

  testlink:
    environment:
      - TESTLINK_ENABLE_API=1
      - TESTLINK_API_ENABLED=1
    ports:
      - "8080:80"

  nginx:
    ports:
      - "80:80"
      - "8080:8080"
      - "8090:8090"
