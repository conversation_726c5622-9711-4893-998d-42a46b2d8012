# Nginx configuration for Test<PERSON>ink Manager and TestLink

upstream testlink_manager {
    server web:8000;
}

upstream testlink_app {
    server testlink:8080;
}

# TestLink Manager (Main application)
server {
    listen 80;
    server_name localhost testlink-manager.local;
    
    # Serve static files
    location /static/ {
        alias /app/staticfiles/;
        expires 30d;
        add_header Cache-Control "public, immutable";
    }
    
    # Serve media files
    location /media/ {
        alias /app/media/;
        expires 30d;
        add_header Cache-Control "public, immutable";
    }
    
    # Proxy to Django application
    location / {
        proxy_pass http://testlink_manager;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_redirect off;
        
        # Increase timeout for long-running requests
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}

# TestLink Application (on port 8080)
server {
    listen 8080;
    server_name localhost testlink.local;
    
    # Increase client max body size for file uploads
    client_max_body_size 100M;
    
    # Proxy to TestLink application
    location / {
        proxy_pass http://testlink_app;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_redirect off;
        
        # Increase timeout for TestLink operations
        proxy_connect_timeout 120s;
        proxy_send_timeout 120s;
        proxy_read_timeout 120s;
    }
}

# Health check endpoint
server {
    listen 8090;
    server_name localhost;
    
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
}
