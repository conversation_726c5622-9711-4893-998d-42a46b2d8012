#!/bin/bash

# TestLink Setup Script

echo "=================================================="
echo "TestLink + TestLink Manager Setup"
echo "=================================================="

# Start services
echo "Starting Docker services..."
docker-compose up -d

echo "Waiting for services to start..."
sleep 30

# Check if TestLink is running
echo "Checking TestLink status..."
until curl -f http://localhost:8080 > /dev/null 2>&1; do
    echo "Waiting for TestLink to be ready..."
    sleep 10
done

echo "TestLink is ready!"

# Check if TestLink Manager is running
echo "Checking TestLink Manager status..."
until curl -f http://localhost:8000 > /dev/null 2>&1; do
    echo "Waiting for TestLink Manager to be ready..."
    sleep 10
done

echo "TestLink Manager is ready!"

echo ""
echo "=================================================="
echo "Setup Complete!"
echo "=================================================="
echo ""
echo "Services are now running:"
echo ""
echo "🔗 TestLink Manager: http://localhost:8000"
echo "   - Admin: admin/admin123"
echo "   - Test Lead: testlead/testpass123"
echo "   - Testers: john/testpass123, jane/testpass123, etc."
echo ""
echo "🔗 TestLink: http://localhost:8080"
echo "   - Default admin: admin/admin"
echo "   - Setup required on first visit"
echo ""
echo "🔗 Nginx Health Check: http://localhost:8090/health"
echo ""
echo "📋 Next Steps:"
echo "1. Visit TestLink (http://localhost:8080) and complete setup"
echo "2. Enable XML-RPC API in TestLink admin"
echo "3. Create API key in TestLink user profile"
echo "4. Update TESTLINK_API_KEY in docker-compose.yml"
echo "5. Restart services: docker-compose restart web"
echo ""
echo "📊 Database Access:"
echo "- TestLink Manager DB: localhost:5432 (testlink_user/testlink_pass)"
echo "- TestLink DB: localhost:3306 (testlink/testlink123)"
echo ""
