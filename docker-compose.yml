version: "3.8"

services:
  # TestLink Manager Django App
  web:
    build: .
    ports:
      - "8000:8000"
    volumes:
      - .:/app
      - static_volume:/app/staticfiles
      - media_volume:/app/media
    environment:
      - DEBUG=True
      - DATABASE_URL=************************************************/testlink_manager
      - TESTLINK_URL=http://testlink:80/lib/api/xmlrpc/v1/xmlrpc.php
      - TESTLINK_API_KEY=testlink_api_key_123
    depends_on:
      - db
      - testlink
    command: >
      sh -c "python manage.py migrate &&
             python manage.py collectstatic --noinput &&
             python manage.py create_test_data &&
             python manage.py runserver 0.0.0.0:8000"

  # PostgreSQL Database for TestLink Manager
  db:
    image: postgres:13
    volumes:
      - postgres_data:/var/lib/postgresql/data/
    environment:
      - POSTGRES_DB=testlink_manager
      - POSTGRES_USER=testlink_user
      - POSTGRES_PASSWORD=testlink_pass
    ports:
      - "5432:5432"

  # TestLink Application
  testlink:
    image: testlink/testlink:latest
    ports:
      - "8080:80"
    volumes:
      - testlink_data:/var/testlink
      - testlink_logs:/var/lib/testlink/logs
      - testlink_upload:/var/lib/testlink/upload_area
    environment:
      - DB_TYPE=mysql
      - DB_HOST=testlink_db
      - DB_NAME=testlink
      - DB_USER=testlink
      - DB_PASS=testlink123
      - DB_PORT=3306
      - TESTLINK_DB_HOST=testlink_db
      - TESTLINK_DB_NAME=testlink
      - TESTLINK_DB_USER=testlink
      - TESTLINK_DB_PASS=testlink123
    depends_on:
      - testlink_db
    restart: unless-stopped

  # MySQL Database for TestLink
  testlink_db:
    image: mysql:8.0
    volumes:
      - testlink_mysql_data:/var/lib/mysql
    environment:
      - MYSQL_ROOT_PASSWORD=root123
      - MYSQL_DATABASE=testlink
      - MYSQL_USER=testlink
      - MYSQL_PASSWORD=testlink123
    ports:
      - "3306:3306"
    command: --default-authentication-plugin=mysql_native_password
    restart: unless-stopped

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/conf.d/default.conf
      - static_volume:/app/staticfiles
      - media_volume:/app/media
    depends_on:
      - web
      - testlink
    restart: unless-stopped

volumes:
  # TestLink Manager volumes
  postgres_data:
  static_volume:
  media_volume:

  # TestLink volumes
  testlink_data:
  testlink_logs:
  testlink_upload:
  testlink_mysql_data:
